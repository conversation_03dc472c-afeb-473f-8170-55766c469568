import NextLink from '@/app/components/ui/NextLink'
import Accordion from '@/app/components/Accordion'
import { servicePreviews } from '@/app/data/servicePreviews'
import Heading from '@/app/components/ui/Heading'

const ServicesSection = () => {
  return (
    <div className="space-y-5">
      <Heading>Need a Hand With Something Specific?</Heading>
      <p>Here’s a glimpse at what we’ve been dreamcrafting lately.</p>
      <Accordion items={servicePreviews} />
      <p>
        Not sure where to start? Our competences are collected to support you at any stage—whether
        you’re still shaping an idea or scaling something people already love.
      </p>
      <p>
        Explore the full <NextLink href="/services">service page</NextLink> to see how we can craft
        your dream from every angle.
      </p>
    </div>
  )
}

export default ServicesSection
