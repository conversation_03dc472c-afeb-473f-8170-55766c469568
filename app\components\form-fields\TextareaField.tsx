import { FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Textarea } from '@/components/ui/textarea'
import { FieldError, FieldErrors, FieldValues, UseFormRegister } from 'react-hook-form'

type TextareaFieldProps = {
  register: UseFormRegister<FieldValues>
  name: string
  label?: string
  placeholder?: string
  error?: FieldError | FieldErrors<FieldValues>
  disabled?: boolean
  className?: string
}

const TextareaField = ({
  register,
  name,
  label,
  placeholder,
  error,
  disabled,
  className,
}: TextareaFieldProps) => {
  return (
    <FormItem>
      {label && <FormLabel htmlFor={name}>{label}</FormLabel>}
      <Textarea
        disabled={disabled}
        {...register(name)}
        placeholder={placeholder}
        rows={5}
        className={className}
      />
      {!disabled && <FormMessage>{error?.message?.toString()}</FormMessage>}
    </FormItem>
  )
}

export default TextareaField
