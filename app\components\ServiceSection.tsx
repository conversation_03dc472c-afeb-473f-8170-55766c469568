import Link from 'next/link'
import { PageFolder } from '@/app/types/types'
import { slugToTitle } from '@/lib/slugToTitle'
import Heading from '@/app/components/ui/Heading'

type ServiceSectionProps = Pick<PageFolder, 'slug' | 'pages'>

const ServiceSection = ({ slug: categorySlug, pages }: ServiceSectionProps) => {
  return (
    <div className="flex flex-col">
      <Heading>
        <Link href={`/services/${categorySlug}`}>{slugToTitle(categorySlug)}</Link>
      </Heading>
      <ul className="m-0">
        {pages.map(({ title, slug }) => (
          <li key={title}>
            <Link href={`/services/${categorySlug}/${slug}`}>{title}</Link>
          </li>
        ))}
      </ul>
    </div>
  )
}

export default ServiceSection
