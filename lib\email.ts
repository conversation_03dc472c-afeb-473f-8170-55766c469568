import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses'
import { NextResponse } from 'next/server'
import { z } from 'zod'

const sesClient = new SESClient({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
})

type SendEmailOptions = {
  subject: string
  body: string
}

export async function sendEmail({ subject, body }: SendEmailOptions) {
  const params = {
    Source: process.env.EMAIL_FROM!,
    Destination: {
      ToAddresses: [process.env.EMAIL_TO!],
    },
    Message: {
      Subject: {
        Data: subject,
      },
      Body: {
        Text: { Data: body },
      },
    },
  }

  const command = new SendEmailCommand(params)
  const response = await sesClient.send(command)

  return response
}

type EmailConfigProps<T> = {
  subject: string
  formType: string
  formatBody: (data: z.infer<z.ZodType<T>>) => string
}

type ProcessEmailRequestProps<T> = {
  req: Request
  schema: z.ZodType<T>
  emailConfig: EmailConfigProps<T>
}

export async function processEmailRequest<T>({
  req,
  schema,
  emailConfig,
}: ProcessEmailRequestProps<T>) {
  try {
    const data: T = await req.json()
    const result = schema.safeParse(data)

    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid form data', details: result.error.issues },
        { status: 400 }
      )
    }

    const messageBody = emailConfig.formatBody(result.data)

    const response = await sendEmail({
      subject: emailConfig.subject,
      body: messageBody,
    })

    return NextResponse.json({
      message: `${emailConfig.formType} successfully processed`,
      messageId: response.MessageId,
    })
  } catch (error) {
    return NextResponse.json(
      {
        error: `Failed to process ${emailConfig.formType.toLowerCase()}`,
        details: (error as Error).message,
      },
      { status: 500 }
    )
  }
}
