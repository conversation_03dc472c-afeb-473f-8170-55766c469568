import { Metadata } from 'next'
import ErrorPage from '@/app/components/ErrorPage'
import { Button } from '@/components/ui/button'
import ContactButton from '@/app/components/ContactButton'
import Link from 'next/link'

export const metadata: Metadata = {
  title: 'Page Not Found',
  description: 'The page you are looking for could not be found.',
}

const NotFoundPage = () => {
  const CTAs = (
    <>
      <Button asChild>
        <Link href="/">Back to safety!</Link>
      </Button>
      <Button asChild>
        <Link href="/services">Explore our dreamcrafting modes!</Link>
      </Button>
      <ContactButton>Share your challenge!</ContactButton>
    </>
  )

  return (
    <ErrorPage
      title="404 – Page Not Found"
      message={`Looks like this room doesn't exist in our online heartquarters yet. It might’ve floated off in a tech stack balloon.
      Want to head back home or explore a new dream?`}
      CTAs={CTAs}
    />
  )
}

export default NotFoundPage
