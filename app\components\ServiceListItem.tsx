import { Service } from '@/app/types/types'
import Image from 'next/image'
import Link from 'next/link'
import Heading from '@/app/components/ui/Heading'

type Props = Pick<Service, 'title' | 'description' | 'thumbnail'> & { path?: string }

const ServiceListItem = ({ title, description, thumbnail, path }: Props) => {
  if (path) {
    return (
      <Link href={path}>
        <ServiceListItemContent title={title} description={description} thumbnail={thumbnail} />
      </Link>
    )
  }

  return <ServiceListItemContent title={title} description={description} thumbnail={thumbnail} />
}

type ServiceListItemContentProps = Pick<Service, 'title' | 'description' | 'thumbnail'>

const ServiceListItemContent = ({ title, description, thumbnail }: ServiceListItemContentProps) => {
  return (
    <div className="flex flex-col gap-4 mx-8 justify-between">
      <Heading as="h3" className="text-base text-gray-700">
        {title}
      </Heading>
      {thumbnail && (
        <div className="rounded aspect-[9/16] w-full bg-white flex-1 flex overflow-hidden">
          <Image
            width="310"
            height="310"
            className="w-full object-contain"
            src={'/images/' + thumbnail}
            alt={title}
          />
        </div>
      )}
      <p className="text-gray-600 m-auto lg:line-clamp-9 whitespace-pre-wrap" title={description}>
        {description}
      </p>
    </div>
  )
}

export default ServiceListItem
