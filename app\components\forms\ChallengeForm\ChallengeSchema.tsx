import { BaseSchema } from '@/app/components/forms/BaseSchema'
import { z } from 'zod'

export const challengeOptions = [
  {
    value: 'misfit-solutions',
    label: "Misfit Solutions: When ready-made options just don't capture your vision.",
  },
  {
    value: 'code-mess',
    label: 'The Code Mess: When custom solutions become a jumbled mess of code.',
  },
  {
    value: 'resource-drain',
    label: 'Resource Drain: When delays threaten both your time and budget.',
  },
  { value: 'momentum-loss', label: 'Momentum Loss: When the initial spark starts to wane.' },
  { value: 'own-challenge', label: 'Your very own challenge:' },
] as const

export type ChallengeValue = (typeof challengeOptions)[number]['value']
export const challengeValues = challengeOptions.map(item => item.value) as [
  ChallengeValue,
  ...ChallengeValue[],
]

export const ChallengeSchema = BaseSchema.extend({
  challenge: z.enum(challengeValues),
  yourChallenge: z.string().optional(),
}).superRefine((data, ctx) => {
  if (
    data.challenge === 'own-challenge' &&
    (!data.yourChallenge || data.yourChallenge.trim().length < 10)
  ) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message:
        'Please describe your challenge when selecting your own challenge at least in a couple of sentences.',
      path: ['yourChallenge'],
    })
  }
})

export type ChallengeFormType = z.infer<typeof ChallengeSchema>
