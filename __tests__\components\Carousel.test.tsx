import React from 'react'
import { render, fireEvent } from '@testing-library/react'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel'
import type { UseEmblaCarouselType } from 'embla-carousel-react'

// Mock embla-carousel-react
const mockScrollNext = jest.fn()
const mockScrollPrev = jest.fn()
const mockSelectedScrollSnap = jest.fn()
const mockOn = jest.fn()
const mockOff = jest.fn()

// Track current slide index
let currentSlideIndex = 0

jest.mock('embla-carousel-react', () => ({
  __esModule: true,
  default: jest.fn(() => [
    jest.fn(), // carouselRef
    {
      canScrollPrev: jest.fn(() => true),
      canScrollNext: jest.fn(() => true),
      scrollPrev: jest.fn(() => {
        mockScrollPrev()
        if (currentSlideIndex > 0) {
          currentSlideIndex--
        }
        mockSelectedScrollSnap.mockReturnValue(currentSlideIndex)
        // Simulate the 'select' event
        const selectCallback = mockOn.mock.calls.find(call => call[0] === 'select')?.[1]
        if (selectCallback) selectCallback()
      }),
      scrollNext: jest.fn(() => {
        mockScrollNext()
        currentSlideIndex++
        mockSelectedScrollSnap.mockReturnValue(currentSlideIndex)
        // Simulate the 'select' event
        const selectCallback = mockOn.mock.calls.find(call => call[0] === 'select')?.[1]
        if (selectCallback) selectCallback()
      }),
      selectedScrollSnap: mockSelectedScrollSnap,
      on: mockOn,
      off: mockOff,
    },
  ]),
}))

describe('Carousel Components', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks()
    // Reset slide index
    currentSlideIndex = 0
    mockSelectedScrollSnap.mockReturnValue(0)
  })
  describe('Basic Carousel', () => {
    it('should render carousel with horizontal orientation by default', () => {
      const { container } = render(
        <Carousel>
          <CarouselContent>
            <CarouselItem>
              <div>Slide 1</div>
            </CarouselItem>
          </CarouselContent>
        </Carousel>
      )

      expect(container).toMatchSnapshot()
    })

    it('should render carousel with custom options', () => {
      const { container } = render(
        <Carousel opts={{ loop: true, align: 'center' }}>
          <CarouselContent>
            <CarouselItem>
              <div>Slide 1</div>
            </CarouselItem>
          </CarouselContent>
        </Carousel>
      )

      expect(container).toMatchSnapshot()
    })
  })

  describe('Single slide carousel', () => {
    it('should render carousel with single slide', () => {
      const { container } = render(
        <Carousel>
          <CarouselContent>
            <CarouselItem>
              <div>Single Slide Content</div>
            </CarouselItem>
          </CarouselContent>
        </Carousel>
      )

      expect(container).toMatchSnapshot()
    })
  })

  describe('Multiple slides carousel', () => {
    it('should render carousel with multiple slides', () => {
      const { container } = render(
        <Carousel>
          <CarouselContent>
            <CarouselItem>
              <div>Slide 1</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 2</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 3</div>
            </CarouselItem>
          </CarouselContent>
        </Carousel>
      )

      expect(container).toMatchSnapshot()
    })

    it('should render carousel with navigation controls', () => {
      const { container } = render(
        <Carousel>
          <CarouselContent>
            <CarouselItem>
              <div>Slide 1</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 2</div>
            </CarouselItem>
          </CarouselContent>
          <CarouselPrevious />
          <CarouselNext />
        </Carousel>
      )

      expect(container).toMatchSnapshot()
    })
  })

  describe('Carousel with different content types', () => {
    it('should render carousel with complex content', () => {
      const { container } = render(
        <Carousel>
          <CarouselContent>
            <CarouselItem>
              <div className="p-4">
                <h3>Card Title 1</h3>
                <p>Card description with some text content.</p>
                <button>Action Button</button>
              </div>
            </CarouselItem>
            <CarouselItem>
              <div className="p-4">
                <h3>Card Title 2</h3>
                <p>Another card with different content.</p>
                <button>Another Button</button>
              </div>
            </CarouselItem>
          </CarouselContent>
        </Carousel>
      )

      expect(container).toMatchSnapshot()
    })
  })

  describe('Carousel orientations', () => {
    it('should render vertical carousel with navigation', () => {
      const { container } = render(
        <Carousel orientation="vertical">
          <CarouselContent>
            <CarouselItem>
              <div>Vertical Slide 1</div>
            </CarouselItem>
            <CarouselItem>
              <div>Vertical Slide 2</div>
            </CarouselItem>
          </CarouselContent>
          <CarouselPrevious />
          <CarouselNext />
        </Carousel>
      )

      expect(container).toMatchSnapshot()
    })
  })

  describe('Carousel with custom classes', () => {
    it('should render carousel with custom className', () => {
      const { container } = render(
        <Carousel className="custom-carousel-class">
          <CarouselContent className="custom-content-class">
            <CarouselItem className="custom-item-class">
              <div>Custom Styled Slide</div>
            </CarouselItem>
          </CarouselContent>
        </Carousel>
      )

      expect(container).toMatchSnapshot()
    })
  })

  describe('Carousel navigation functionality', () => {
    it('should call scrollNext when next button is clicked and move to next slide', () => {
      let carouselApi: UseEmblaCarouselType[1]
      const { getByRole } = render(
        <Carousel
          setApi={api => {
            carouselApi = api
          }}
        >
          <CarouselContent>
            <CarouselItem>
              <div>Slide 1</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 2</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 3</div>
            </CarouselItem>
          </CarouselContent>
          <CarouselNext />
          <CarouselPrevious />
        </Carousel>
      )

      // Initial position should be 0
      expect(carouselApi?.selectedScrollSnap()).toBe(0)

      const nextButton = getByRole('button', { name: /next slide/i })
      fireEvent.click(nextButton)

      expect(mockScrollNext).toHaveBeenCalledTimes(1)
      expect(carouselApi?.selectedScrollSnap()).toBe(1)
    })

    it('should call scrollPrev when previous button is clicked and move to previous slide', () => {
      let carouselApi: UseEmblaCarouselType[1]
      const { getByRole } = render(
        <Carousel
          setApi={api => {
            carouselApi = api
          }}
        >
          <CarouselContent>
            <CarouselItem>
              <div>Slide 1</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 2</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 3</div>
            </CarouselItem>
          </CarouselContent>
          <CarouselNext />
          <CarouselPrevious />
        </Carousel>
      )

      // First move to slide 1
      const nextButton = getByRole('button', { name: /next slide/i })
      fireEvent.click(nextButton)
      expect(carouselApi?.selectedScrollSnap()).toBe(1)

      // Then move back to slide 0
      const prevButton = getByRole('button', { name: /previous slide/i })
      fireEvent.click(prevButton)

      expect(mockScrollPrev).toHaveBeenCalledTimes(1)
      expect(carouselApi?.selectedScrollSnap()).toBe(0)
    })

    it('should handle multiple clicks and track slide position correctly', () => {
      let carouselApi: UseEmblaCarouselType[1]
      const { getByRole } = render(
        <Carousel
          setApi={api => {
            carouselApi = api
          }}
        >
          <CarouselContent>
            <CarouselItem>
              <div>Slide 1</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 2</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 3</div>
            </CarouselItem>
          </CarouselContent>
          <CarouselNext />
          <CarouselPrevious />
        </Carousel>
      )

      const nextButton = getByRole('button', { name: /next slide/i })
      const prevButton = getByRole('button', { name: /previous slide/i })

      // Initial position
      expect(carouselApi?.selectedScrollSnap()).toBe(0)

      // Click next button multiple times
      fireEvent.click(nextButton) // Should be at slide 1
      expect(carouselApi?.selectedScrollSnap()).toBe(1)

      fireEvent.click(nextButton) // Should be at slide 2
      expect(carouselApi?.selectedScrollSnap()).toBe(2)

      fireEvent.click(nextButton) // Should be at slide 3
      expect(carouselApi?.selectedScrollSnap()).toBe(3)

      // Click previous button multiple times
      fireEvent.click(prevButton) // Should be at slide 2
      expect(carouselApi?.selectedScrollSnap()).toBe(2)

      fireEvent.click(prevButton) // Should be at slide 1
      expect(carouselApi?.selectedScrollSnap()).toBe(1)

      expect(mockScrollNext).toHaveBeenCalledTimes(3)
      expect(mockScrollPrev).toHaveBeenCalledTimes(2)
    })

    it('should scroll through all slides sequentially', () => {
      let carouselApi: UseEmblaCarouselType[1]
      const { getByRole } = render(
        <Carousel
          setApi={api => {
            carouselApi = api
          }}
        >
          <CarouselContent>
            <CarouselItem>
              <div>Slide 1</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 2</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 3</div>
            </CarouselItem>
            <CarouselItem>
              <div>Slide 4</div>
            </CarouselItem>
          </CarouselContent>
          <CarouselNext />
          <CarouselPrevious />
        </Carousel>
      )

      const nextButton = getByRole('button', { name: /next slide/i })
      const prevButton = getByRole('button', { name: /previous slide/i })

      // Start at slide 0
      expect(carouselApi?.selectedScrollSnap()).toBe(0)

      // Navigate forward through all slides
      fireEvent.click(nextButton)
      expect(carouselApi?.selectedScrollSnap()).toBe(1)

      fireEvent.click(nextButton)
      expect(carouselApi?.selectedScrollSnap()).toBe(2)

      fireEvent.click(nextButton)
      expect(carouselApi?.selectedScrollSnap()).toBe(3)

      // Navigate backward through all slides
      fireEvent.click(prevButton)
      expect(carouselApi?.selectedScrollSnap()).toBe(2)

      fireEvent.click(prevButton)
      expect(carouselApi?.selectedScrollSnap()).toBe(1)

      // Verify function call counts
      expect(mockScrollNext).toHaveBeenCalledTimes(3)
      expect(mockScrollPrev).toHaveBeenCalledTimes(2)
    })
  })
})
