import type { MDXComponents } from 'mdx/types'
import ContactButton from '@/app/components/ContactButton'
import Link from 'next/link'
import isExternalLink from '@/lib/isExternalLink'

export function useMDXComponents(components: MDXComponents): MDXComponents {
  return {
    a: (props) => {
      const { href, children } = props
      const external = isExternalLink(href)

      return (
        <Link 
          href={href} 
          target={external ? "_blank" : undefined}
          rel={external ? "noopener noreferrer" : undefined}
          {...props}
        >
          {children}
        </Link>
      )},
    ContactButton,
    ...components,
  }
}
