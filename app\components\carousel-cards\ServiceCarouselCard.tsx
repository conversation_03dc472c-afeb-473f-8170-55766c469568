import ServiceListItem from '@/app/components/ServiceListItem'
import CarouselCard from '@/app/components/carousel-cards/CarouselCard'
import { CarouselProps } from '@/components/ui/carousel'
import { PageFolder } from '@/app/types/types'

type Props = {
  services: PageFolder[]
  plugins: CarouselProps['plugins']
  setApi: CarouselProps['setApi']
  stopAutoplay: () => void
  startAutoplay: () => void
}

const ServiceCarouselCard = ({ services, plugins, setApi, stopAutoplay, startAutoplay }: Props) => {
  return (
    <CarouselCard
      title="What we love to do:"
      className="w-full h-auto xl:h-[26rem]"
      plugins={plugins}
      setApi={setApi}
      onMouseEnter={stopAutoplay}
      onMouseLeave={startAutoplay}
    >
      {services.flatMap(({ slug: categorySlug, title, description, pages }) => [
        <ServiceListItem
          key={`category-${categorySlug}`}
          title={title}
          description={description}
          path={`/services/${categorySlug}`}
        />,
        ...pages.map(({ slug, title, description }) => (
          <ServiceListItem
            key={`page-${categorySlug}-${slug}`}
            title={title}
            description={description}
            path={`/services/${categorySlug}/${slug}`}
          />
        )),
      ])}
    </CarouselCard>
  )
}

export default ServiceCarouselCard
