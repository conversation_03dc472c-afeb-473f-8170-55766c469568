'use client'

import { useEffect, useRef, ReactNode } from 'react'

const EmailLink = ({ children }: { children: ReactNode }) => {
  const emailLinkRef = useRef<HTMLAnchorElement>(null)
  const email = 'manystack'
  const domain = 'manystack.com'
  const subject = encodeURIComponent('My dream')

  useEffect(() => {
    if (!emailLinkRef.current) return

    emailLinkRef.current.setAttribute('href', '')

    const parts = ['mailto:', email, '@', domain, '?subject=', subject]
    let currentIndex = 0

    const timer = setInterval(() => {
      if (currentIndex < parts.length) {
        const currentHref = emailLinkRef.current?.getAttribute('href') || ''
        emailLinkRef.current?.setAttribute('href', currentHref + parts[currentIndex])
        currentIndex++
      } else {
        clearInterval(timer)
      }
    }, 100)

    return () => clearInterval(timer)
  }, [email, domain, subject])

  return (
    <a ref={emailLinkRef} href="#email-section">
      {children}
    </a>
  )
}

export default EmailLink
