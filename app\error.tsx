'use client'

import ErrorPage from '@/app/components/ErrorPage'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

const Error = ({ reset }: { reset: () => void }) => {
  const CTAs = (
    <>
      <Button onClick={() => reset()}>Try again</Button>
      <Button asChild>
        <Link href="/">Back to homepage!</Link>
      </Button>
    </>
  )

  return (
    <ErrorPage
      title="500 – Internal Server Error"
      message={`Our gears are jammed! The cloudcrafting crew is on it.
      Give us a moment or head back while we tidy things up.`}
      CTAs={CTAs}
    />
  )
}

export default Error
