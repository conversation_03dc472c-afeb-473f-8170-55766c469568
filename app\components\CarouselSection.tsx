'use client'

import CarouselColumn from '@/app/components/CarouselColumn'
import { useRandomCarouselScrollManager } from '@/app/hooks/useRandomCarouselScrollManager'
import { PageFolder, Project, Feedback } from '@/app/types/types'
import ProjectCarouselCard from '@/app/components/carousel-cards/ProjectCarouselCard'
import ServiceCarouselCard from '@/app/components/carousel-cards/ServiceCarouselCard'
import ClientFeedbackCarouselCard from '@/app/components/carousel-cards/ClientFeedbackCarouselCard'

type Props = {
  services: PageFolder[]
  clientFeedback: Feedback[]
  projects: Project[]
}

const CarouselSection = ({ services, clientFeedback, projects }: Props) => {
  const { plugins, setApi, startAutoplay, stopAutoplay, activeCarouselIndexes } =
    useRandomCarouselScrollManager([services, clientFeedback, projects])

  return (
    <div className="grid-in-carousel contents xl:grid xl:grid-cols-2 items-center justify-center gap-x-10 overflow-hidden h-fit xl:sticky top-0">
      <CarouselColumn className="max-xl:contents mx-auto *:max-w-[65ch] [&>*:first-child]:grid-in-service [&>*:last-child]:grid-in-feedback">
        <ServiceCarouselCard
          services={services}
          plugins={[plugins[0]]}
          setApi={setApi(0)}
          stopAutoplay={stopAutoplay}
          startAutoplay={startAutoplay}
        />
        <ClientFeedbackCarouselCard
          clientFeedback={clientFeedback}
          plugins={[plugins[1]]}
          setApi={setApi(1)}
          stopAutoplay={stopAutoplay}
          startAutoplay={startAutoplay}
        />
      </CarouselColumn>
      <CarouselColumn className="max-xl:contents mx-auto *:max-w-[65ch] [&>*:first-child]:grid-in-project">
        <ProjectCarouselCard
          projects={projects}
          plugins={[plugins[2]]}
          setApi={setApi(2)}
          stopAutoplay={stopAutoplay}
          startAutoplay={startAutoplay}
          activeCarouselIndex={activeCarouselIndexes[2]}
        />
      </CarouselColumn>
    </div>
  )
}

export default CarouselSection
