import { ComponentProps, useMemo } from 'react'
import { cn } from '@/lib/utils'

type Props = ComponentProps<'h1'> & {
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
}

const Heading = ({ as: Tag = 'h2', ...props }: Props) => {
  const headingClassName = useMemo(() => {
    switch (Tag) {
      case 'h1':
        return 'text-5xl font-bold'
      case 'h2':
        return 'text-2xl font-bold'
      case 'h3':
        return 'text-xl font-semibold'
      case 'h4':
        return 'text-lg font-semibold'
      case 'h5':
        return 'text-base font-semibold'
      case 'h6':
        return 'text-sm font-semibold'
      default:
        return 'text-base font-bold'
    }
  }, [Tag])

  return <Tag {...props} className={cn(headingClassName, props.className)} />
}

export default Heading
