import { Project } from '@/app/types/types'
import Image from 'next/image'
import Link from 'next/link'
import Heading from '@/app/components/ui/Heading'

type Props = Pick<Project, 'title' | 'description' | 'thumbnail' | 'slug'>

const ProjectListItem = ({ title, description, thumbnail, slug }: Props) => {
  if (slug) {
    return (
      <Link href={'/projects/' + slug}>
        <ProjectListItemContent title={title} description={description} thumbnail={thumbnail} />
      </Link>
    )
  }

  return <ProjectListItemContent title={title} description={description} thumbnail={thumbnail} />
}

type ProjectListItemContentProps = Pick<Project, 'title' | 'description' | 'thumbnail'>

const ProjectListItemContent = ({ title, description, thumbnail }: ProjectListItemContentProps) => {
  return (
    <div className="flex flex-col gap-4 mx-8 justify-between">
      <Heading as="h3" className="text-base text-gray-700">
        {title}
      </Heading>
      {thumbnail && (
        <div className="rounded aspect-[9/16] w-full bg-white flex-1 flex overflow-hidden">
          <Image
            width="310"
            height="310"
            className="w-full object-contain"
            src={'/images/' + thumbnail}
            alt={title}
          />
        </div>
      )}
      <p className="text-gray-600">{description}</p>
    </div>
  )
}

export default ProjectListItem
