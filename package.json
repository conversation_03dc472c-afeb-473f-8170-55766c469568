{"name": "manystack.com", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --check .", "format:fix": "prettier --write .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:update-snapshots": "jest --updateSnapshot"}, "dependencies": {"@aws-sdk/client-ses": "^3.731.1", "@hookform/resolvers": "^3.10.0", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "15.1.5", "@next/third-parties": "^15.1.6", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.1.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/canvas-confetti": "^1.9.0", "@types/jest": "^29.5.14", "@types/mdx": "^2.0.13", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dlx": "^0.2.1", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0", "lodash": "^4.17.21", "lucide-react": "^0.473.0", "next": "15.1.5", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.54.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.1", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@savvywombat/tailwindcss-grid-areas": "^4.0.0", "@tailwindcss/typography": "^0.5.16", "@testing-library/dom": "^10.4.0", "@types/lodash": "^4.17.7", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "19.0.7", "@types/react-dom": "19.0.3", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "autoprefixer": "^10", "eslint": "^9.18.0", "eslint-config-next": "15.1.5", "eslint-config-prettier": "^10.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "postcss": "^8", "prettier": "^3.5.3", "string-length": "4.0.2", "strip-ansi": "6.0.1", "tailwindcss": "^3", "typescript": "^5"}, "resolutions": {"@types/react": "19.0.7", "@types/react-dom": "19.0.3", "strip-ansi": "^6.0.1", "string-length": "^4.0.2"}}