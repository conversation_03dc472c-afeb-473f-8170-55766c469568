import {
  EmailSubscriptionFormType,
  EmailSubscriptionSchema,
} from '@/app/components/forms/EmailSubscriptionForm/EmailSubscriptionSchema'
import { processEmailRequest } from '@/lib/email'

export async function POST(req: Request) {
  return processEmailRequest<EmailSubscriptionFormType>({
    req,
    schema: EmailSubscriptionSchema,
    emailConfig: {
      subject: 'Email subscription from manystack.com',
      formType: 'Email subscription request',
      formatBody: data =>
        `Name: ${data.name}

        Email: ${data.email}

        Additional Notes: ${data.additionalNotes}

      
        This user has subscribed to receive email content.`,
    },
  })
}
