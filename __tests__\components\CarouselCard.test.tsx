import React from 'react'
import { render } from '@testing-library/react'
import CarouselCard from '@/app/components/carousel-cards/CarouselCard'

// Mock embla-carousel-react - NEEDED for CarouselCard component
jest.mock('embla-carousel-react', () => ({
  __esModule: true,
  default: jest.fn(() => [
    jest.fn(), // carouselRef
    {
      canScrollPrev: jest.fn(() => true),
      canScrollNext: jest.fn(() => true),
      scrollPrev: jest.fn(),
      scrollNext: jest.fn(),
      on: jest.fn(),
      off: jest.fn(),
    },
  ]),
}))

describe('CarouselCard Component', () => {
  describe('Basic rendering', () => {
    it('should render carousel card with single child', () => {
      const { container } = render(
        <CarouselCard>
          <div>Single Child Content</div>
        </CarouselCard>
      )

      expect(container).toMatchSnapshot()
    })

    it('should render carousel card with multiple children', () => {
      const { container } = render(
        <CarouselCard>
          <div>Child 1</div>
          <div>Child 2</div>
          <div>Child 3</div>
        </CarouselCard>
      )

      expect(container).toMatchSnapshot()
    })
  })

  describe('With title', () => {
    it('should render carousel card with title', () => {
      const { container } = render(
        <CarouselCard title="Test Carousel Title">
          <div>Content with title</div>
        </CarouselCard>
      )

      expect(container).toMatchSnapshot()
    })

    it('should render carousel card with title and multiple children', () => {
      const { container } = render(
        <CarouselCard title="Multiple Items Carousel">
          <div>Item 1</div>
          <div>Item 2</div>
          <div>Item 3</div>
        </CarouselCard>
      )

      expect(container).toMatchSnapshot()
    })
  })

  describe('Without title', () => {
    it('should render carousel card without title', () => {
      const { container } = render(
        <CarouselCard>
          <div>Content without title</div>
          <div>Another item</div>
        </CarouselCard>
      )

      expect(container).toMatchSnapshot()
    })
  })

  describe('With overlay', () => {
    it('should render carousel card with overlay', () => {
      const overlay = (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <span className="text-white">Overlay Content</span>
        </div>
      )

      const { container } = render(
        <CarouselCard overlay={overlay}>
          <div>Content with overlay</div>
          <div>Another item</div>
        </CarouselCard>
      )

      expect(container).toMatchSnapshot()
    })

    it('should render carousel card with title and overlay', () => {
      const overlay = (
        <div className="absolute top-0 right-0 p-2">
          <button>Close</button>
        </div>
      )

      const { container } = render(
        <CarouselCard title="Carousel with Overlay" overlay={overlay}>
          <div>Item 1</div>
          <div>Item 2</div>
        </CarouselCard>
      )

      expect(container).toMatchSnapshot()
    })
  })

  describe('With custom className', () => {
    it('should render carousel card with custom className and title', () => {
      const { container } = render(
        <CarouselCard className="w-full h-96" title="Custom Styled Carousel">
          <div>Item 1</div>
          <div>Item 2</div>
        </CarouselCard>
      )

      expect(container).toMatchSnapshot()
    })
  })

  describe('Navigation visibility', () => {
    it('should not show navigation controls with single child', () => {
      const { container } = render(
        <CarouselCard>
          <div>Single item - no navigation needed</div>
        </CarouselCard>
      )

      expect(container).toMatchSnapshot()
    })

    it('should show navigation controls with multiple children', () => {
      const { container } = render(
        <CarouselCard>
          <div>Item 1</div>
          <div>Item 2</div>
        </CarouselCard>
      )

      expect(container).toMatchSnapshot()
    })
  })

  describe('Event handlers', () => {
    it('should render carousel card with event handlers', () => {
      const mockMouseEnter = jest.fn()
      const mockMouseLeave = jest.fn()

      const { container } = render(
        <CarouselCard onMouseEnter={mockMouseEnter} onMouseLeave={mockMouseLeave}>
          <div>Content with event handlers</div>
          <div>Another item</div>
        </CarouselCard>
      )

      expect(container).toMatchSnapshot()
    })
  })

  describe('Complex content scenarios', () => {
    it('should render carousel card with complex nested content', () => {
      const { container } = render(
        <CarouselCard title="Complex Content Carousel">
          <div className="p-4 bg-white rounded shadow">
            <h3 className="text-lg font-bold">Card 1</h3>
            <p className="text-gray-600">Description for card 1</p>
            <button className="mt-2 px-4 py-2 bg-blue-500 text-white rounded">Action</button>
          </div>
          <div className="p-4 bg-gray-100 rounded shadow">
            <h3 className="text-lg font-bold">Card 2</h3>
            <p className="text-gray-600">Description for card 2</p>
            <div className="flex gap-2 mt-2">
              <button className="px-3 py-1 bg-green-500 text-white rounded">Yes</button>
              <button className="px-3 py-1 bg-red-500 text-white rounded">No</button>
            </div>
          </div>
        </CarouselCard>
      )

      expect(container).toMatchSnapshot()
    })
  })
})
