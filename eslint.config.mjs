import { FlatCompat } from '@eslint/eslintrc'

const compat = new FlatCompat({
  baseDirectory: import.meta.dirname,
})

const eslintConfig = [
  ...compat.config({
    extends: [
      'next/core-web-vitals',
      'next/typescript',
      'plugin:@typescript-eslint/recommended',
      'plugin:react-hooks/recommended',
      'plugin:jsx-a11y/recommended',
      'plugin:import/recommended',
      'plugin:import/typescript',
      'prettier',
    ],
    plugins: [
      '@typescript-eslint',
      'prettier',
      'react-hooks',
      'jsx-a11y',
      'import',
      'unused-imports',
    ],
    parser: '@typescript-eslint/parser',
    rules: {
      'prettier/prettier': [
        'error',
        {
          trailingComma: 'es5',
          arrowParens: 'avoid',
          endOfLine: 'auto',
          tabWidth: 2,
          singleQuote: true,
          semi: false,
          eolLast: true,
          printWidth: 100,
        },
      ],

      // React
      'react/react-in-jsx-scope': 'off',
      'react/prop-types': 'off',
      'react/function-component-definition': [2, { namedComponents: 'arrow-function' }],
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
  }),
]

export default eslintConfig
