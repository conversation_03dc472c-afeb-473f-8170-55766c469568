import { Step } from '@/app/types/types'

export const steps: Step[] = [
  {
    title: 'Step 1: Turning Dreams into Concepts',
    content: `Your vision is sharp, and you know what's needed out there. Together, we focus on concept validation, making sure your ideas are spot on, and tech stack advising, which is just us helping you pick the best tools to get the job done.
- Your Role: You share your aspirations and insights.
- Our Part: We listen with care and start crafting a plan using your insights and our tech guidance.`,
  },
  {
    title: 'Step 2: Creating a Flexible Plan',
    content: `Together, we build a roadmap focusing on your dream's core elements and minimum viable features—those first few important features to get your app up and running fast!
- Your Job: Choose which parts matter most.
- Our Job: Transform those choices into a clear action plan rooted in the lean startup cycle, which is just a fancy way of saying we’ll keep things simple and adaptable.`,
  },
  {
    title: 'Step 3: Building the MVP and Learning as We Go',
    content: `We quickly put together an MVP to highlight your idea's strengths. Using rapid iteration—meaning we tweak and improve constantly—early feedback is our guide.
- Your Input: Lead early checks and use feedback to keep your goals in sight.
- Our Role: Quickly craft and refine using creativity and feedback.`,
  },
  {
    title: 'Step 4: Refining with Each Cycle',
    content: `Each round teaches us more about your users, helping us fine-tune your application.
- Your Task: Chat with users, gather insights to guide your dream's growth.
- Our Role: Adapt the product swiftly, molding it around your vision.`,
  },
  {
    title: 'Step 5: Reaching New Levels Together',
    content: `As we ascend upwards, we improve your dreams and grab new opportunities.
- Your Vision: Steer the direction, adjusting based on fresh insights.
- Our Part: Deliver strong, adaptable solutions to pave the way for growth.`,
  },
]
