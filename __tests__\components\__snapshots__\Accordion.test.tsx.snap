// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Accordion Component Content variations should render with long text content 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="vertical"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="vertical"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="vertical"
        data-state="open"
      >
        <button
          aria-controls="radix-:rn:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="vertical"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:rm:"
          type="button"
        >
          Item with Long Content
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:rm:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="vertical"
        data-state="open"
        id="radix-:rn:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 120px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  This is a very long text content that should test how the accordion handles extensive text. This is a very long text content that should test how the accordion handles extensive text. This is a very long text content that should test how the accordion handles extensive text. This is a very long text content that should test how the accordion handles extensive text. This is a very long text content that should test how the accordion handles extensive text. This is a very long text content that should test how the accordion handles extensive text. This is a very long text content that should test how the accordion handles extensive text. This is a very long text content that should test how the accordion handles extensive text. This is a very long text content that should test how the accordion handles extensive text. This is a very long text content that should test how the accordion handles extensive text. 
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Accordion Component Content variations should render with special characters and formatting 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="vertical"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="vertical"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="vertical"
        data-state="open"
      >
        <button
          aria-controls="radix-:rp:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="vertical"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:ro:"
          type="button"
        >
          Special Characters & Formatting
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:ro:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="vertical"
        data-state="open"
        id="radix-:rp:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 120px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  Text with
new lines

And special chars: @#$%^&*()
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Accordion Component Empty state should match snapshot for empty items array 1`] = `<div />`;

exports[`Accordion Component Multiple items accordion should render multiple items in horizontal orientation (xl screen) 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:rb:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:ra:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:ra:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:rb:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 120px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  This is the content for test item 1.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rd:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:rc:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:rc:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:rd:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 120px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rf:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:re:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:re:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="closed"
        hidden=""
        id="radix-:rf:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 120px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Component Multiple items accordion should render multiple items in vertical orientation (mobile) 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="vertical"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="vertical"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="vertical"
        data-state="open"
      >
        <button
          aria-controls="radix-:r5:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="vertical"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r4:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r4:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="vertical"
        data-state="open"
        id="radix-:r5:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 120px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  This is the content for test item 1.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="vertical"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="vertical"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r7:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="vertical"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r6:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r6:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="vertical"
        data-state="closed"
        hidden=""
        id="radix-:r7:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 120px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="vertical"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="vertical"
        data-state="closed"
      >
        <button
          aria-controls="radix-:r9:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="vertical"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:r8:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r8:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="vertical"
        data-state="closed"
        hidden=""
        id="radix-:r9:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 120px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Component Screen size variations should render correctly when screen size is undefined 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="vertical"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="vertical"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="vertical"
        data-state="open"
      >
        <button
          aria-controls="radix-:rh:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="vertical"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:rg:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:rg:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="vertical"
        data-state="open"
        id="radix-:rh:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 120px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  This is the content for test item 1.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="vertical"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="vertical"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rj:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="vertical"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:ri:"
          type="button"
        >
          Test Item 2
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:ri:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="vertical"
        data-state="closed"
        hidden=""
        id="radix-:rj:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 120px;"
      />
    </div>
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="vertical"
      data-state="closed"
    >
      <h3
        class="flex"
        data-orientation="vertical"
        data-state="closed"
      >
        <button
          aria-controls="radix-:rl:"
          aria-expanded="false"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="vertical"
          data-radix-collection-item=""
          data-state="closed"
          id="radix-:rk:"
          type="button"
        >
          Test Item 3
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:rk:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="vertical"
        data-state="closed"
        hidden=""
        id="radix-:rl:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 120px;"
      />
    </div>
  </div>
</div>
`;

exports[`Accordion Component Single item accordion should render single item in horizontal orientation (xl screen) 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="horizontal"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="horizontal"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="horizontal"
        data-state="open"
      >
        <button
          aria-controls="radix-:r3:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="horizontal"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r2:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r2:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="horizontal"
        data-state="open"
        id="radix-:r3:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 120px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  This is the content for test item 1.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Accordion Component Single item accordion should render single item in vertical orientation (mobile) 1`] = `
<div>
  <div
    class="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
    data-orientation="vertical"
  >
    <div
      class="data-[orientation=vertical]:border-b data-[orientation=horizontal]:border-r grid grid-flow-row xl:grid-flow-col"
      data-orientation="vertical"
      data-state="open"
    >
      <h3
        class="flex"
        data-orientation="vertical"
        data-state="open"
      >
        <button
          aria-controls="radix-:r1:"
          aria-disabled="true"
          aria-expanded="true"
          class="flex flex-1 items-center justify-between text-sm font-medium transition-all hover:underline text-left [&[data-orientation=horizontal][data-state=closed]>svg]:-rotate-90 [&[data-orientation=horizontal][data-state=open]>svg]:rotate-90 [&[data-orientation=vertical][data-state=closed]>svg]:rotate-0 [&[data-orientation=vertical][data-state=open]>svg]:rotate-180 xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2"
          data-orientation="vertical"
          data-radix-collection-item=""
          data-state="open"
          id="radix-:r0:"
          type="button"
        >
          Test Item 1
          <svg
            data-testid="chevron-down"
          />
        </button>
      </h3>
      <div
        aria-labelledby="radix-:r0:"
        class="overflow-hidden text-sm data-[orientation=vertical]:data-[state=closed]:animate-accordion-up data-[orientation=vertical]:data-[state=open]:animate-accordion-down data-[orientation=horizontal]:data-[state=closed]:animate-accordion-right"
        data-orientation="vertical"
        data-state="open"
        id="radix-:r1:"
        role="region"
        style="--radix-accordion-content-height: var(--radix-collapsible-content-height); --radix-accordion-content-width: var(--radix-collapsible-content-width); transition-duration: 0s; animation-name: none; --radix-collapsible-content-height: 120px; --radix-collapsible-content-width: 120px;"
      >
        <div
          class="pb-4 pt-0 xl:h-96 xl:p-0"
        >
          <div
            class="relative overflow-hidden h-full"
            dir="ltr"
            style="position: relative; --radix-scroll-area-corner-width: 0px; --radix-scroll-area-corner-height: 0px; width: 100%;"
          >
            <style>
              [data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}
            </style>
            <div
              class="h-full w-full rounded-[inherit]"
              data-radix-scroll-area-viewport=""
              style="overflow-x: hidden; overflow-y: scroll;"
            >
              <div
                style="min-width: 100%; display: table;"
              >
                <div
                  class="text-gray-600 whitespace-pre-wrap p-2"
                >
                  This is the content for test item 1.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
