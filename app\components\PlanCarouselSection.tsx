'use client'

import { steps } from '@/app/data/steps'
import CarouselCard from '@/app/components/carousel-cards/CarouselCard'
import { useRandomCarouselScrollManager } from '@/app/hooks/useRandomCarouselScrollManager'
import ServiceListItem from '@/app/components/ServiceListItem'

const PlanCarouselSection = () => {
  const { plugins, setApi, startAutoplay, stopAutoplay } = useRandomCarouselScrollManager([steps])

  return (
    <CarouselCard
      className="w-full h-fit"
      plugins={[plugins[0]]}
      setApi={setApi(0)}
      onMouseEnter={stopAutoplay}
      onMouseLeave={startAutoplay}
    >
      {steps.map(({ title, content }) => (
        <ServiceListItem key={title} title={title} description={content} />
      ))}
    </CarouselCard>
  )
}

export default PlanCarouselSection
