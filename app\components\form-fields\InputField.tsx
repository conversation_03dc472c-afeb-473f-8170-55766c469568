import { FormDescription, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { FieldError, FieldErrors, FieldValues, UseFormRegister } from 'react-hook-form'

type InputFieldProps = {
  register: UseFormRegister<FieldValues>
  name: string
  label?: string
  description?: string
  placeholder?: string
  error?: FieldError | FieldErrors<FieldValues>
}

const InputField = ({
  register,
  name,
  label,
  description,
  placeholder,
  error,
}: InputFieldProps) => {
  return (
    <FormItem>
      {label && <FormLabel htmlFor={name}>{label}</FormLabel>}
      <Input {...register(name)} placeholder={placeholder} />
      {description && <FormDescription>{description}</FormDescription>}
      <FormMessage>{error?.message?.toString()}</FormMessage>
    </FormItem>
  )
}

export default InputField
