'use client'

import React from 'react'
import {
  ChallengeFormType,
  ChallengeSchema,
} from '@/app/components/forms/ChallengeForm/ChallengeSchema'
import { postChallenge } from '@/lib/contact/post'
import Header from '@/app/components/forms/Header'
import Form from '@/app/components/forms/Form'
import ChallengeFormFields from '@/app/components/forms/ChallengeForm/ChallengeFormFields'

type Props = {
  onSuccessfulSubmission: () => void
}

const ChallengeForm = ({ onSuccessfulSubmission }: Props) => {
  const onSubmit = async (values: ChallengeFormType) => {
    await postChallenge(values)
    onSuccessfulSubmission()
  }

  const header = (
    <Header title="We Care About Your Toughest Challenges">
      Do you think a particular difficulty can keep your dream project from ascending into the
      cloud?
      <br />
      Share your top challenge with us, and together we’ll turn it into a stepping stone on your
      dreamcrafting adventure.
      <br />
      Your dream deserves the loveliest online heartquarters, and we’re here to help you build it.
    </Header>
  )

  return (
    <Form
      validationSchema={ChallengeSchema}
      defaultValues={{
        name: '',
        email: '',
        challenge: 'own-challenge',
        yourChallenge: '',
        agreement: false,
      }}
      onSubmit={onSubmit}
      header={header}
      submitButtonText="Help me tackle my challenge!"
    >
      <ChallengeFormFields />
    </Form>
  )
}

export default ChallengeForm
