'use client'

import { useState, useEffect } from 'react'
import resolveConfig from 'tailwindcss/resolveConfig'
import tailwindConfig from '@/tailwind.config'
import { useWindowSize } from 'usehooks-ts'

const fullConfig = resolveConfig(tailwindConfig)
const xlScreenWidth = parseInt(fullConfig.theme.screens.xl)

export const useIsXlScreen = () => {
  const { width: windowWidth } = useWindowSize()
  const [isXlScreen, setIsXlScreen] = useState<boolean>()

  useEffect(() => {
    if (windowWidth !== undefined) {
      setIsXlScreen(windowWidth >= xlScreenWidth)
    }
  }, [windowWidth])

  return isXlScreen
}
