'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { PageFolder } from '@/app/types/types'
import BreadcrumbItem from '@/app/components/BreadcrumbItem'
import isHomepage from '@/lib/isHomepage'

const Breadcrumb = ({ pages }: { pages: PageFolder[] }) => {
  const currentPath = usePathname()
  const pathSegments = currentPath.split('/').filter(Boolean)

  if (isHomepage(currentPath)) return null

  return (
    <nav className="text-3xl bg-gray-100 p-10" aria-label="Breadcrumb">
      <ol className="flex flex-wrap [&>li]:after:content-['>'] [&>li]:after:mx-2 [&>li:last-of-type]:after:content-none">
        <li>
          <Link href="/">manystack</Link>
        </li>
        {pathSegments.map((segment, index) => (
          <li key={segment}>
            <BreadcrumbItem
              segment={segment}
              index={index}
              pathSegments={pathSegments}
              pages={pages}
            />
          </li>
        ))}
      </ol>
    </nav>
  )
}

export default Breadcrumb
