'use client'

import Link from 'next/link'
import { slugToTitle } from '@/lib/slugToTitle'
import { PageFolder } from '@/app/types/types'
import { useCallback, useMemo } from 'react'

type Props = {
  segment: string
  index: number
  pathSegments: string[]
  pages: PageFolder[]
}

const BreadcrumbItem = ({ segment, index, pathSegments, pages }: Props) => {
  const pagePath = pathSegments.slice(0, index + 1).join('/')

  const findSegmentTitle = useCallback(
    (pagePath: string, segment: string): string => {
      const matchedFolder = pages.find(({ slug }) => pagePath.startsWith(slug))
      if (!matchedFolder) return slugToTitle(segment)

      const pathSegments = pagePath.split(matchedFolder.slug + '/')[1]
      if (!pathSegments) return matchedFolder.title

      const matchedPage = matchedFolder.pages.find(({ slug }) => slug === pathSegments)
      return matchedPage?.title || slugToTitle(segment)
    },
    [pages]
  )

  const pageTitle = useMemo(() => {
    if (index === 0) {
      return slugToTitle(segment)
    }
    const subPath = pathSegments.slice(1, index + 1).join('/')
    return findSegmentTitle(subPath, segment)
  }, [index, segment, pathSegments, findSegmentTitle])

  return (
    <Link href={`/${pagePath}`} aria-current="page">
      {pageTitle}
    </Link>
  )
}

export default BreadcrumbItem
