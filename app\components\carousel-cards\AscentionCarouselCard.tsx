'use client'

import { useRandomCarouselScrollManager } from '@/app/hooks/useRandomCarouselScrollManager'
import { ascensionLogs } from '@/app/data/ascensionLogs'
import CarouselCard from '@/app/components/carousel-cards/CarouselCard'
import AscensionLogItem from '@/app/components/AscensionLogItem'

const AscentionCarouselCard = () => {
  const { plugins, setApi, startAutoplay, stopAutoplay } = useRandomCarouselScrollManager([
    ascensionLogs,
  ])

  return (
    <CarouselCard
      className="w-full h-auto"
      plugins={[plugins[0]]}
      setApi={setApi(0)}
      onMouseEnter={stopAutoplay}
      onMouseLeave={startAutoplay}
    >
      {ascensionLogs.map(({ id, title, description }) => (
        <AscensionLogItem key={id} title={title} description={description} />
      ))}
    </CarouselCard>
  )
}

export default AscentionCarouselCard
