import { Separator } from '@/components/ui/separator'
import { FacebookIcon, LinkedinIcon, TwitterIcon } from 'lucide-react'
import React from 'react'
import IconBubble from '@/app/components/IconBubble'
import Link from 'next/link'
import ContactButton from '@/app/components/ContactButton'
import EmailLink from '@/app/components/EmailLink'
import FooterHeading from '@/app/components/ui/FooterHeading'

const Footer = () => {
  return (
    <footer className="w-full bg-gray-200 p-14 xl:p-20 text-gray-600 [&_a]:underline">
      <nav className="w-fit flex gap-10 xl:gap-32 mx-auto flex-col lg:flex-row">
        <ul>
          <li>
            <FooterHeading className="!text-3xl [&_a]:no-underline">
              <Link href="/">manystack</Link>
            </FooterHeading>
          </li>
        </ul>
        <ul>
          <li>
            <FooterHeading>Keep Sharing Your Dreams</FooterHeading>
          </li>
          <li>
            <EmailLink>Drop an email</EmailLink>
          </li>
        </ul>
        <ul>
          <li>
            <FooterHeading>Quick Links</FooterHeading>
          </li>
          <li className="*:h-fit *:!text-gray-600 *:!font-normal">
            <ContactButton variant="link">Your Story</ContactButton>
          </li>
          <li>
            <Link href="/projects">Our Projects</Link>
          </li>
          <li>
            <Link href="/services">Our Services</Link>
          </li>
        </ul>
        <ul>
          <li>
            <FooterHeading>Legal</FooterHeading>
          </li>
          <li>
            <Link href="/policies/privacy-promise">Privacy Promise</Link>
          </li>
          <li>
            <Link href="/policies/friendly-terms">Friendly Terms</Link>
          </li>
        </ul>
        <ul>
          <li>
            <FooterHeading>Let’s Keep in Touch</FooterHeading>
          </li>
          <li>
            <div className="flex gap-2">
              <IconBubble icon={FacebookIcon} link="https://facebook.com/mnystck" />
              <IconBubble icon={TwitterIcon} link="https://x.com/mnystck" />
              <IconBubble icon={LinkedinIcon} link="https://www.linkedin.com/company/manystack" />
            </div>
          </li>
        </ul>
      </nav>
      <Separator className="my-14 xl:my-20 bg-gray-300" />
      <p className="text-center text-sm">
        &copy; {new Date().getFullYear()} Manystack. All rights reserved.
      </p>
    </footer>
  )
}

export default Footer
