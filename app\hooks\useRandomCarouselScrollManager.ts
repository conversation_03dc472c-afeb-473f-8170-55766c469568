import { CarouselApi } from '@/components/ui/carousel'
import Autoplay from 'embla-carousel-autoplay'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'

export const useRandomCarouselScrollManager = (
  dataArrays: unknown[][],
  autoSlideDelay: number = 7000
) => {
  const apiRefs = useRef<(CarouselApi | undefined)[]>([])
  const [apiInitialized, setApiInitialized] = useState(false)
  const pluginsRef = useRef<ReturnType<typeof Autoplay>[]>([])
  const [activeCarouselIndexes, setActiveCarouselIndexes] = useState<number[]>([])
  const activeCarousel = useRef(0)
  const firstPlay = useRef(false)
  const initialized = useRef(false)

  if (!initialized.current) {
    pluginsRef.current = dataArrays.map(() =>
      Autoplay({ delay: autoSlideDelay, stopOnInteraction: true, playOnInit: false })
    )
    setActiveCarouselIndexes(dataArrays.map(() => 0))
    initialized.current = true
  }

  const setApi = useCallback(
    (index: number) => (api: CarouselApi | undefined) => {
      apiRefs.current[index] = api
      setApiInitialized(true)
    },
    []
  )

  const validDataArrays = useMemo(() => {
    const validDataArrays = dataArrays
      .map((data, index) => ({ valid: data && data.length > 1, index }))
      .filter(item => item.valid)
      .map(item => item.index)
    return validDataArrays
  }, [dataArrays])

  const stopAllCarousels = useCallback(() => {
    validDataArrays.forEach(index => {
      pluginsRef.current[index]?.stop()
    })
  }, [validDataArrays])

  const playCarousel = useCallback((nextCarouselIndex: number) => {
    pluginsRef.current[nextCarouselIndex]?.play()
  }, [])

  const selectRandomCarousel = useCallback(() => {
    let availableCarousels = [...validDataArrays]
    if (activeCarousel.current !== null && availableCarousels.length > 1) {
      availableCarousels = availableCarousels.filter(n => n !== activeCarousel.current)
    }
    activeCarousel.current =
      availableCarousels[Math.floor(Math.random() * availableCarousels.length)]
  }, [activeCarousel, validDataArrays])

  const stopAutoplay = useCallback(() => {
    stopAllCarousels()
  }, [stopAllCarousels])

  const startAutoplay = useCallback(() => {
    playCarousel(activeCarousel.current)
  }, [playCarousel])

  useEffect(() => {
    if (!apiInitialized) return

    const unbinds = apiRefs.current.map((api, index) => {
      if (!api) return () => {}
      api.on('select', () => {
        setActiveCarouselIndexes(prev => {
          prev[index] = api.selectedScrollSnap()
          return [...prev]
        })
      })
      return () => api.off('select', () => {})
    })

    return () => {
      unbinds.forEach(unbind => unbind())
    }
  }, [apiInitialized])

  useEffect(() => {
    if (!apiInitialized) return

    const handleSelect = () => {
      stopAllCarousels()
      selectRandomCarousel()
      playCarousel(activeCarousel.current)
    }

    const unbinds = apiRefs.current.map(api => {
      if (!api) return () => {}
      api.on('autoplay:select', handleSelect)
      return () => api.off('autoplay:select', handleSelect)
    })

    return () => {
      unbinds.forEach(unbind => unbind())
    }
  }, [apiInitialized, stopAllCarousels, playCarousel, selectRandomCarousel])

  useEffect(() => {
    if (!apiInitialized) return
    if (firstPlay.current) return
    firstPlay.current = true

    selectRandomCarousel()
    playCarousel(activeCarousel.current)
  }, [apiInitialized, playCarousel, selectRandomCarousel])

  return {
    plugins: pluginsRef.current,
    setApi,
    stopAutoplay,
    startAutoplay,
    activeCarouselIndexes,
  }
}
