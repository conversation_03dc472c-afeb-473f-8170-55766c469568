'use client'

import React from 'react'
import {
  EmailSubscriptionFormType,
  EmailSubscriptionSchema,
} from '@/app/components/forms/EmailSubscriptionForm/EmailSubscriptionSchema'
import { postEmailSubscription } from '@/lib/contact/post'
import Header from '@/app/components/forms/Header'
import Form from '@/app/components/forms/Form'
import EmailSubscriptionFormFields from '@/app/components/forms/EmailSubscriptionForm/EmailSubscriptionFormFields'

type Props = {
  onSuccessfulSubmission: () => void
}

const EmailSubscriptionForm = ({ onSuccessfulSubmission }: Props) => {
  const onSubmit = async (values: EmailSubscriptionFormType) => {
    await postEmailSubscription(values)
    onSuccessfulSubmission()
  }

  const header = (
    <Header title="Need a Spark of Inspiration?">
      Got an amazing idea that shouldn’t gather dust?
      <br />
      Let us send you our neat little guide to help you gather the right resources for a SaaS
      implementation and bring your dream setup to life in just 30 days.
      <br />
      You’ve got this!
    </Header>
  )

  return (
    <Form
      validationSchema={EmailSubscriptionSchema}
      defaultValues={{
        name: '',
        email: '',
        additionalNotes: '',
        agreement: false,
      }}
      onSubmit={onSubmit}
      header={header}
      submitButtonText="Let me chart my concept!"
    >
      <EmailSubscriptionFormFields />
    </Form>
  )
}

export default EmailSubscriptionForm
