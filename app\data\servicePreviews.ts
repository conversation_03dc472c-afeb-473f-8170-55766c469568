import { ServicePreview } from '@/app/types/types'

export const servicePreviews: ServicePreview[] = [
  {
    id: '11b5a023-d889-4bd5-92e3-00ab73f0a5bf',
    title: '✨ Make It Look Right (Frontend Magic)',
    text: `For dreambuilders who want beauty, smoothness, and user delight.

UI/UX Design
Users feel lost in clunky apps. We design intuitive, inviting interfaces that make your online heartquarters feel like home. With Manystack's frontend magic, users explore more and return often.

React Development
When your app feels stiff or outdated, we bring it to life with React. We craft components that evolve and respond instantly. What do you get? Happy users and a UI that grows with your dream.

Mobile UI Design
If your app feels off on mobile, our dreamcraftsmen are here to shape designs built for thumbs and swipes. We turn friction into flow, so every interaction feels smooth, wherever users encounter them.

Chakra UI
Messy styling? We use Chakra UI to decorate your app with balance and elegance. It’s all about harmony—consistent components that feel right and look stunning.

Vite
Waiting for reloads kills your creative rhythm. Vite fires up development with instant previews and joyful iteration. Your project stays vibrant—and never slows down.`,
  },
  {
    id: '21b5a023-d889-4bd5-92e3-00ab73f0a5bf',
    title: '🔧 Make It Work Right (Backend Power)',
    text: `For those struggling with laggy, clunky, or complicated app logic.

Backend Development
Legacy systems acting up? We dive deep, refactor smart, and bring clarity to your backend. You’ll gain speed, fewer bugs, and breathing room for big ideas.

Node.js
Is your app dragging under the weight? We power it with Node.js for real-time performance and smart scaling. Users enjoy snappy responses, and you get room to grow.

Laravel
Overwhelmed by backend clutter? We use Laravel to streamline logic and supercharge development. Your dream app becomes flexible, stable, and easy to evolve.

Symfony
Unclear architecture slowing things down? Symfony gives your app a graceful, powerful backbone. With Manystack, you're building solid structures, not tech debt.`,
  },
  {
    id: '31b5a023-d889-4bd5-92e3-00ab73f0a5bf',
    title: '🧹 Make It Tidy & Clean (Data Wizardry)',
    text: `For dreambuilders buried under messy data and lost insights.

Database Management
If your data’s scattered and sluggish, we transform it into a fortress of clarity. Our dreamcraftsmen optimize every layer so your app stays light, fast, and reliable.

MySQL & PostgreSQL
Tangled tables and broken queries? We structure your data with care and precision. It’s like alphabetizing your library—only way faster.

MongoDB
Stuck with rigid data models? We bring in MongoDB’s flexible collections to match your evolving features. Build fast, pivot freely, and scale at your own pace.

Elasticsearch
When users can’t find what they need, we flip on the searchlight. With Elasticsearch, we unlock speed and discovery—turning frustration into delight.`,
  },
  {
    id: '41b5a023-d889-4bd5-92e3-00ab73f0a5bf',
    title: '📱 Make It Work Everywhere (App Everywhere)',
    text: `For dreambuilders who want cross-platform access with one build.

Mobile App Development
Users expect mobile magic. We deliver buttery-smooth apps for all devices, built to feel native and act fast. From onboarding to checkout, it just works.

Cross-Platform Development
Why build twice? With shared code and smart design, we craft one app that rules iOS and Android alike. You launch quicker and look sharper.

React Native Development
Two codebases, double the trouble? React Native cuts the clutter. We unify development so your app reaches more people—with less tech stress.

Expo
Early-stage? Tight timeline? Expo is your MVP rocket fuel. We ship fast, test quickly, and keep updates flowing—all without the usual mobile headaches.`,
  },
  {
    id: '51b5a023-d889-4bd5-92e3-00ab73f0a5bf',
    title: '☁️ Make It Fly (Cloud & Deployment)',
    text: `For dreambuilders needing launchpad-level support.

Docker
Deployments feel fragile? Docker wraps your app in secure containers that work anywhere. No more surprise crashes—just smooth flying.

AWS
Scaling makes you nervous? We build your cloud home on AWS, with security and elasticity baked in. Your dream stays grounded—even as it soars.

Vercel
Your Next.js app deserves speed. Vercel launches it globally with edge-first rendering and cloud brilliance. It’s fast, stable, and ready to impress.

Netlify
Updates dragging? Netlify automates your flow—just push and go live. Hosting, builds, and deploys in one place. Light and clean.

Cloud Development
Still clinging to clunky old workflows? We bring modern cloudcrafting to your setup, with painless deploys, fast scaling, and full flexibility.

SaaS & Cloud Apps
Your product has big potential. We build scalable SaaS tools that grow automatically with your users. Integration, security, automation—it’s all built in.`,
  },
  {
    id: '61b5a023-d889-4bd5-92e3-00ab73f0a5bf',
    title: '🔍 Make It Reliable (Testing & Syncing)',
    text: `For those who’ve been burned by bugs and version chaos.

Smart Testing
Things break quietly—until users notice. We build test suites that catch the cracks early, so you launch stable, confident, and calm.

Git
Losing work or redoing code? We guide you through clean Git flows that keep everything versioned and in sync—like a safety net for your build.

CircleCI
Manual deploys slowing the team? With CircleCI, we automate the boring stuff—tests, builds, releases—so your dream keeps moving.

Cypress
UI bugs are sneaky. We run full frontend tests with Cypress to mimic real clicks and paths. That means smoother UX and fewer surprises.

Jest & Vitest
Logic flaws sink confidence. We test everything behind the scenes so users get a consistent, joyful experience—no matter how complex it gets.`,
  },
  {
    id: '71b5a023-d889-4bd5-92e3-00ab73f0a5bf',
    title: '🚀 Make It Real (MVP & Speed Boosters)',
    text: `For early-stage dreambuilders wanting to get their app idea off the ground fast.

Expo
Your app idea is ready—but the tech feels like a wall. We use Expo to break through. From first sketch to live app, it's simple, swift, and magical.

Vite
Waiting for builds is soul-crushing. We bring in Vite to turbocharge development—meaning faster feedback, more momentum, and happier devs.

Laravel & Symfony
Need a backend fast? We match you with Laravel for quick iteration or Symfony for complex builds. Either way, your foundation is strong.

SQL & MongoDB
Choosing the wrong data stack slows you down later. We pick the right one now—relational or flexible—so you can grow without backtracking.`,
  },
]
