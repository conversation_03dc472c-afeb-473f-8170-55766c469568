import { ReactNode } from 'react'
import Heading from '@/app/components/ui/Heading'

type Props = {
  title: string
  message: string
  CTAs?: ReactNode
}

const ErrorPage = ({ title, message, CTAs }: Props) => {
  return (
    <main className="h-screen flex flex-col justify-center text-center gap-4">
      <Heading as="h1">{title}</Heading>
      <div className="flex flex-col items-center gap-8 text-gray-500">
        <p className="whitespace-pre-line">{message}</p>
        {CTAs && <div className="flex flex-col items-center gap-4 *:w-fit">{CTAs}</div>}
      </div>
    </main>
  )
}

export default ErrorPage
