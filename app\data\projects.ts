import { Project } from '@/app/types/types'

export const projects: Project[] = [
  {
    id: '11b5a023-d889-4bd5-92e3-00ab73f0a5bf',
    title: 'Diobox',
    description:
      'We empowered event leaders with cutting-edge tools like the latest React technology, a Visual Email Builder, and fun features like Drag-and-Drop Guest Seating to boost productivity and style.',
    website: 'https://home.d.io',
    thumbnail: 'diobox.png',
    slug: 'diobox',
    order: 4,
  },
  {
    id: '21b5a023-d889-4bd5-92e3-00ab73f0a5bf',
    title: 'Airteam',
    description:
      'For Airbnb and VRBO hosts, we crafted a toolkit that offers simple property management with features like smart scheduling and easy invoicing, all aimed to boost efficiency.',
    website: 'https://apps.apple.com/hu/app/airteam-app/id1607066907?platform=iphone',
    thumbnail: 'airteam.png',
    order: 3,
  },
  {
    id: '9566ac16-c0f1-4238-a382-3531754112e4',
    title: 'Influencers.ae',
    description:
      'Future brands and influencers came together through this awesome tool. Built by Manystack, it makes working together easy and helps grow successful partnerships.',
    website: 'https://influencers.ae',
    thumbnail: 'influencers-ae.png',
    order: 2,
  },
  {
    id: '41b5a023-d889-4bd5-92e3-00ab73f0a5bf',
    title: 'Recruiterly',
    description:
      'Leading the way in recruitment. With Manystack’s help, they created an impactful platform that uses smart data insights, honest reviews, and strategic planning to revolutionize how recruitment works.',
    website: 'https://www.linkedin.com/company/recruiterly/about/',
    thumbnail: 'recruiterly.png',
    slug: 'recruiterly',
    order: 1,
  },
]
