import { Metadata } from 'next'
import Roadblocks from '@/app/components/Roadblocks'
import AscensionLogs from '@/app/components/AscensionLogs'
import ApplicationDevelopmentIntro from '@/app/components/ApplicationDevelopmentIntro'
import PlanSection from '@/app/components/PlanSection'
import ServicesSection from '@/app/components/ServicesSection'
import Hero from '@/app/components/Hero'

export const metadata: Metadata = {
  title: 'manystack - Building your dream app for web and mobile',
  description:
    'From sketch to launch, your dream app deserves a team that gets it. Whether web or mobile, Manystack builds every layer with care—ready to ascend your concept into the cloud and into the hands of real users.',
}

const Home = () => {
  return (
    <>
      <section className="grid-in-hero">
        <Hero />
      </section>
      <section className="grid-in-roadblocks max-xl:mt-10">
        <Roadblocks />
      </section>
      <section className="grid-in-ascension-logs">
        <AscensionLogs />
      </section>
      <section className="grid-in-application-development-intro">
        <ApplicationDevelopmentIntro />
      </section>
      <section className="grid-in-cards-plan">
        <PlanSection />
      </section>
      <section className="grid-in-service-accordion">
        <ServicesSection />
      </section>
    </>
  )
}

export default Home
