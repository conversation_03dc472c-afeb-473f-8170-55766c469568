import { z } from 'zod'
import { BaseSchema } from '@/app/components/forms/BaseSchema'

export const projectTypeOptions = [
  { value: 'build-from-scratch', label: 'Building something new from scratch.' },
  { value: 'enhance-existing-systems', label: 'Completing/Reshaping/Expanding current systems' },
] as const

export type ProjectTypeValues = (typeof projectTypeOptions)[number]['value']
export const projectTypeValues = projectTypeOptions.map(item => item.value) as [
  ProjectTypeValues,
  ...ProjectTypeValues[],
]

export const CooperationSchema = BaseSchema.extend({
  projectType: z.enum([...projectTypeValues]),
  message: z.string().min(10, {
    message: 'Please summarize your project in a couple of sentences.',
  }),
})

export type CooperationFormType = z.infer<typeof CooperationSchema>
