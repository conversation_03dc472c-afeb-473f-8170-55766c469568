import { Children, ReactNode } from 'react'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  CarouselProps,
} from '@/components/ui/carousel'
import CarouselListItem from '@/app/components/CarouselItem'
import { cn } from '@/lib/utils'
import Heading from '@/app/components/ui/Heading'

type Props = {
  children: ReactNode
  title?: string
  overlay?: ReactNode
  plugins?: CarouselProps['plugins']
  setApi?: CarouselProps['setApi']
  className?: string
  onMouseEnter?: () => void
  onMouseLeave?: () => void
}

const CarouselCard = ({
  children,
  className,
  title,
  overlay,
  plugins,
  setApi,
  onMouseEnter,
  onMouseLeave,
}: Props) => {
  return (
    <Carousel
      className={cn('grid w-80 h-80', title ? 'grid-rows-[auto_1fr]' : 'grid-rows-1', className)}
      opts={{ loop: true }}
      plugins={plugins}
      setApi={setApi}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      onTouchStart={onMouseEnter}
      onTouchEnd={onMouseLeave}
    >
      {title && <Heading className="text-xl py-6">{title}</Heading>}
      <div className="relative rounded bg-gray-200 flex min-h-0 group overflow-hidden">
        {Children.count(children) > 1 && (
          <CarouselPrevious className="z-10 left-3 !pointer-events-auto" />
        )}
        <CarouselContent className="flex items-center h-full">
          {Children.map(children, child => (
            <CarouselItem className="h-full select-none">
              <CarouselListItem>{child}</CarouselListItem>
            </CarouselItem>
          ))}
        </CarouselContent>
        {overlay}
        {Children.count(children) > 1 && (
          <CarouselNext className="z-10 left-[calc(100%-2.75rem)] !pointer-events-auto" />
        )}
      </div>
    </Carousel>
  )
}

export default CarouselCard
