'use client'

import React from 'react'
import {
  CooperationFormType,
  CooperationSchema,
} from '@/app/components/forms/CooperationForm/CooperationSchema'
import { postCooperation } from '@/lib/contact/post'
import Header from '@/app/components/forms/Header'
import Form from '@/app/components/forms/Form'
import CooperationFormFields from '@/app/components/forms/CooperationForm/CooperationFormFields'

type Props = {
  onSuccessfulSubmission: () => void
}

const CooperationForm = ({ onSuccessfulSubmission }: Props) => {
  const onSubmit = async (values: CooperationFormType) => {
    await postCooperation(values)
    onSuccessfulSubmission()
  }

  const header = (
    <Header title="Ready to Dream Big Together?">
      Imagine what we could create if you joined our playful alliance!
      <br />
      Whether you’re eyeing a partnership, looking to build something new, or aiming to
      complete/reshape/expand an existing system, let’s chat.
      <br />
      Our cloudcrafting skills can help your vision reach new heights.
    </Header>
  )

  return (
    <Form
      validationSchema={CooperationSchema}
      defaultValues={{
        name: '',
        email: '',
        projectType: 'build-from-scratch',
        message: '',
        agreement: false,
      }}
      onSubmit={onSubmit}
      header={header}
      submitButtonText="Let's build dreams!"
    >
      <CooperationFormFields />
    </Form>
  )
}

export default CooperationForm
