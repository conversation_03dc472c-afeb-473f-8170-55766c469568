# Jest Snapshot Testing Guide

This document provides comprehensive information about the Jest snapshot tests implemented for the Accordion and Carousel components in manystack.com project.

## Overview

1. **Accordion Component** (`app/components/Accordion.tsx`)
2. **Carousel Components** (`components/ui/carousel.tsx` and `app/components/carousel-cards/CarouselCard.tsx`)

## Test Coverage

### Accordion Component Tests

**Location**: `__tests__/components/Accordion.test.tsx`

**Test Scenarios**:

#### Rendering Tests (Snapshot-based)
- ✅ **Empty state**: Renders null when items array is empty
- ✅ **Single item accordion**: Vertical orientation (mobile) and horizontal orientation (xl screen)
- ✅ **Multiple items accordion**: Vertical orientation (mobile) and horizontal orientation (xl screen)
- ✅ **Screen size variations**: Undefined screen size handling
- ✅ **Content variations**: Long text content and special characters with formatting

#### Interaction Functionality Tests (Behavior-based)
- ✅ **Initial state verification**: First item open by default, others closed with correct `data-state` and `aria-expanded` attributes
- ✅ **Click to open/close**: Clicking on a trigger opens that item and closes the previously open item
- ✅ **Multiple clicks tracking**: Sequential clicking through multiple items with proper state management
- ✅ **Horizontal orientation interaction**: Click behavior works correctly in horizontal orientation (xl screen)

**Key Testing Features**:
- **State Verification**: Checks both `data-state` attributes on AccordionItems and `aria-expanded` attributes on triggers
- **Real User Interaction**: Uses `fireEvent.click()` to simulate actual user clicks on accordion triggers
- **Accordion Behavior**: Verifies single-item-open behavior (clicking one item closes others)
- **Cross-orientation Support**: Tests interaction in both vertical (mobile) and horizontal (xl screen) orientations
- **Helper Function**: Uses `getAccordionItems()` helper for consistent element selection

**Mock Implementation**:
- Mocks `useIsXlScreen`, `useResizeObserver`, `lodash.min`, and `getInnerWidth`
- Provides realistic screen size and resize behavior simulation
- Maintains proper accordion state management during interactions

**Total Tests**: 13 tests with 8 snapshots

### Carousel Component Tests

**Location**: `__tests__/components/Carousel.test.tsx`

**Test Scenarios**:

#### Rendering Tests (Snapshot-based)
- ✅ Basic carousel with horizontal orientation (default)
- ✅ Carousel with custom options
- ✅ Single slide carousel
- ✅ Multiple slides carousel
- ✅ Carousel with navigation controls
- ✅ Carousel with complex content
- ✅ Vertical carousel with navigation
- ✅ Carousel with custom class

#### Navigation Functionality Tests (Behavior-based)
- ✅ **Next button navigation**: Verifies `scrollNext()` is called and carousel moves to next slide
- ✅ **Previous button navigation**: Verifies `scrollPrev()` is called and carousel moves to previous slide
- ✅ **Multiple clicks tracking**: Tests sequential navigation through multiple slides with position verification
- ✅ **Full sequential navigation**: Tests complete forward and backward navigation through all slides

**Key Testing Features**:
- **Function Call Verification**: Ensures embla carousel API methods are invoked
- **Actual Slide Position Tracking**: Verifies carousel moves to correct slide indices using `selectedScrollSnap()`
- **State Management**: Tests proper slide index tracking throughout navigation
- **Bidirectional Movement**: Validates both forward and backward navigation
- **API Integration**: Uses `setApi` to access carousel API for position verification

**Mock Implementation**:
- Enhanced embla-carousel-react mock with realistic slide index tracking
- Simulates actual carousel behavior including slide position updates
- Triggers 'select' events to mimic real carousel state changes

**Total Tests**: 12 tests with 8 snapshots

### CarouselCard Component Tests

**Location**: `__tests__/components/CarouselCard.test.tsx`

**Test Scenarios**:
- ✅ Basic rendering with single child
- ✅ Basic rendering with multiple children
- ✅ Carousel card with title
- ✅ Carousel card with title and multiple children
- ✅ Carousel card without title
- ✅ Carousel card with overlay
- ✅ Carousel card with title and overlay
- ✅ Carousel card with custom className
- ✅ Carousel card with custom className and title
- ✅ Navigation visibility (single vs multiple children)
- ✅ Event handlers
- ✅ Complex nested content scenarios

**Total Tests**: 13 tests with 13 snapshots

## Running Tests

### Run All Tests
```bash
npm test
```

### Run Tests in Watch Mode
```bash
npm run test:watch
```

### Run Tests with Coverage
```bash
npm run test:coverage
```

### Run Specific Test File
```bash
npm test __tests__/components/Accordion.test.tsx
npm test __tests__/components/Carousel.test.tsx
npm test __tests__/components/CarouselCard.test.tsx
```
