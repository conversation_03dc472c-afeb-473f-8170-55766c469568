import '@testing-library/jest-dom'

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Mock window.ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// Mock getBoundingClientRect
Element.prototype.getBoundingClientRect = jest.fn(() => ({
  width: 120,
  height: 120,
  top: 0,
  left: 0,
  bottom: 0,
  right: 0,
  x: 0,
  y: 0,
  toJSON: jest.fn(),
}))

// Mock window.getComputedStyle
window.getComputedStyle = jest.fn(() => ({
  width: '120px',
  paddingLeft: '0px',
  paddingRight: '0px',
  borderLeftWidth: '0px',
  borderRightWidth: '0px',
  marginLeft: '0px',
  marginRight: '0px',
  getPropertyValue: jest.fn(prop => {
    const styles = {
      width: '120px',
      'padding-left': '0px',
      'padding-right': '0px',
      'border-left-width': '0px',
      'border-right-width': '0px',
      'margin-left': '0px',
      'margin-right': '0px',
    }
    return styles[prop] || ''
  }),
}))

// Mock scrollTo
window.scrollTo = jest.fn()

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  ChevronDown: () => <svg data-testid="chevron-down" />,
  ArrowLeft: () => <svg data-testid="arrow-left" />,
  ArrowRight: () => <svg data-testid="arrow-right" />,
}))

// Mock next/font
jest.mock('next/font/google', () => ({
  Inter: () => ({
    className: 'mocked-inter-font',
  }),
}))
