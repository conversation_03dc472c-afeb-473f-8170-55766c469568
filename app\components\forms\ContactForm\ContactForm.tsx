'use client'

import React from 'react'
import { ContactFormType, ContactSchema } from '@/app/components/forms/ContactForm/ContactSchema'
import { postContact } from '@/lib/contact/post'
import Header from '@/app/components/forms/Header'
import Form from '@/app/components/forms/Form'
import ContactFormFields from '@/app/components/forms/ContactForm/ContactFormFields'

type Props = {
  onSuccessfulSubmission: () => void
}

const ContactForm = ({ onSuccessfulSubmission }: Props) => {
  const onSubmit = async (values: ContactFormType) => {
    await postContact(values)
    onSuccessfulSubmission()
  }

  const header = (
    <Header title="Let's Build It!">
      We&apos;re excited to hear about your project!
      <br />
      Please tell us about it and we&apos;ll
      <br />
      get back to you as soon as possible.
    </Header>
  )

  return (
    <Form
      validationSchema={ContactSchema}
      defaultValues={{
        email: '',
        message: '',
      }}
      onSubmit={onSubmit}
      header={header}
    >
      <ContactFormFields />
    </Form>
  )
}

export default ContactForm
