import { ContactFormType, ContactSchema } from '@/app/components/forms/ContactForm/ContactSchema'
import { processEmailRequest } from '@/lib/email'

export async function POST(req: Request) {
  return processEmailRequest<ContactFormType>({
    req,
    schema: ContactSchema,
    emailConfig: {
      subject: 'Inquiry from manystack.com',
      formType: 'Contact request',
      formatBody: data =>
        `Email: ${data.email}

        Message: ${data.message}`,
    },
  })
}
