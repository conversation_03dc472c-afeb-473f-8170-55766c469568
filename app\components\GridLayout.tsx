'use client'

import { usePathname } from 'next/navigation'
import isHomepage from '@/lib/isHomepage'
import { ReactNode } from 'react'
import { PageFolder, Feedback, Project } from '@/app/types/types'
import CarouselSection from '@/app/components/CarouselSection'
import { cn } from '@/lib/utils'

type Props = {
  children: ReactNode
  services: PageFolder[]
  clientFeedback: Feedback[]
  projects: Project[]
}

const GridLayout = ({ children, services, clientFeedback, projects }: Props) => {
  const pathname = usePathname()
  const isHome = isHomepage(pathname)

  return (
    <div
      className={cn(
        isHome ? 'grid-areas-layout-home-mobile' : 'grid-areas-layout-mobile',
        'grid grid-cols-layout-mobile xl:grid-cols-layout xl:grid-areas-layout w-fit mx-auto px-10 pb-10 gap-x-10 *:max-w-4xl flex-1'
      )}
    >
      <div className="grid-in-content max-xl:contents *:max-w-[65ch] mx-auto *:py-10 divide-y-2 divide-bg-border max-xl:[&>*:last-child]:!mb-10 max-xl:[&>*:last-child]:!border-b-2">
        {children}
      </div>
      <CarouselSection services={services} clientFeedback={clientFeedback} projects={projects} />
    </div>
  )
}

export default GridLayout
