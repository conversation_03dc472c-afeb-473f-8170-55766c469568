'use client'

import React from 'react'
import { useFormContext } from 'react-hook-form'
import TextareaField from '@/app/components/form-fields/TextareaField'
import InputField from '@/app/components/form-fields/InputField'

const ContactFormFields = () => {
  const {
    register,
    formState: { errors },
  } = useFormContext()

  return (
    <>
      <InputField
        name={'email'}
        label={'What email can we use for getting back to you?'}
        placeholder={'<EMAIL>'}
        description={"We'll never share your email with anyone else."}
        register={register}
        error={errors.email}
      />
      <TextareaField
        name={'message'}
        label={'Message'}
        placeholder={'Share any extra thoughts here!'}
        register={register}
        error={errors.message}
      />
    </>
  )
}

export default ContactFormFields
