import { Metadata } from 'next'
import { clientFeedback } from '@/app/data/clientFeedback'
import { orderBy } from 'lodash'
import ClientFeedbackListItem from '@/app/components/ClientFeedbackListItem'
import Heading from '@/app/components/ui/Heading'

export const metadata: Metadata = {
  title: 'Client Feedback',
}

const ClientFeedbackPage = () => {
  return (
    <div className="flex flex-col justify-center gap-10">
      <Heading as="h1">Client Feedback</Heading>
      <ul className="space-y-6">
        {orderBy(clientFeedback, 'order').map(({ id, text, client }) => (
          <li className="rounded p-8 bg-gray-200" key={id}>
            <ClientFeedbackListItem text={text} client={client} />
          </li>
        ))}
      </ul>
    </div>
  )
}

export default ClientFeedbackPage
