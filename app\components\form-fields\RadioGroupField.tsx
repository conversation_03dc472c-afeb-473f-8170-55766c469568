import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Control, FieldError, FieldErrors, FieldValues } from 'react-hook-form'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'

type RadioGroupFieldProps = {
  name: string
  control: Control<FieldValues>
  options: readonly { readonly value: string; readonly label: string }[]
  label?: string
  error?: FieldError | FieldErrors<FieldValues>
}

const RadioGroupField = ({ control, options, name, label, error }: RadioGroupFieldProps) => {
  return (
    <>
      <FormField
        control={control}
        name={name}
        render={({ field: { onChange, value } }) => (
          <FormItem className="space-y-3">
            {label && <FormLabel>{label}</FormLabel>}
            <FormControl>
              <RadioGroup
                onValueChange={onChange}
                defaultValue={value}
                className="flex flex-col space-y-2"
              >
                {options.map(({ label, value }) => (
                  <FormItem key={value} className="flex items-center space-x-3 space-y-0">
                    <FormControl>
                      <RadioGroupItem value={value} id={value} />
                    </FormControl>
                    <FormLabel htmlFor={value} className="font-normal cursor-pointer">
                      {label}
                    </FormLabel>
                  </FormItem>
                ))}
              </RadioGroup>
            </FormControl>
          </FormItem>
        )}
      />
      <FormMessage>{error?.message?.toString()}</FormMessage>
    </>
  )
}

export default RadioGroupField
