export const getInnerWidth = (element: HTMLElement | null) => {
  if (!element) return 0
  const computedStyle = window.getComputedStyle(element)
  const width = parseFloat(computedStyle.width)
  const paddingLeft = parseFloat(computedStyle.paddingLeft)
  const paddingRight = parseFloat(computedStyle.paddingRight)
  const borderLeftWidth = parseFloat(computedStyle.borderLeftWidth)
  const borderRightWidth = parseFloat(computedStyle.borderRightWidth)
  const marginLeft = parseFloat(computedStyle.marginLeft)
  const marginRight = parseFloat(computedStyle.marginRight)

  return (
    width -
    paddingLeft -
    paddingRight -
    borderLeftWidth -
    borderRightWidth -
    marginLeft -
    marginRight
  )
}
