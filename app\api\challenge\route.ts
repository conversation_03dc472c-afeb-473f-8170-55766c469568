import {
  ChallengeFormType,
  ChallengeSchema,
} from '@/app/components/forms/ChallengeForm/ChallengeSchema'
import { processEmailRequest } from '@/lib/email'

export async function POST(req: Request) {
  return processEmailRequest<ChallengeFormType>({
    req,
    schema: ChallengeSchema,
    emailConfig: {
      subject: 'Challenge Submission from manystack.com',
      formType: 'Challenge request',
      formatBody: data =>
        `Name: ${data.name}

        Email: ${data.email}

        Challenge Type: ${data.challenge}

        ${data.challenge === 'own-challenge' ? 'Challenge Description: ' + data.yourChallenge : ''}

        
        This user has shared a challenge they'd like help with.`,
    },
  })
}
