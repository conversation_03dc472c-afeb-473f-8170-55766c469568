'use client'

import {
  Accordion as Accordion<PERSON>ontainer,
  AccordionContent,
  AccordionTrigger,
  AccordionItem,
} from '@/components/ui/accordion'
import { useIsXlScreen } from '@/app/hooks/useIsXlScreen'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useCallback, useEffect, useRef, useState, RefObject } from 'react'
import { min } from 'lodash'
import { useResizeObserver } from 'usehooks-ts'
import { getInnerWidth } from '@/lib/getInnerWidth'

type Props = {
  items: { id: string; title: string; text: string }[]
}

const Accordion = ({ items = [] }: Props) => {
  const isXlScreen = useIsXlScreen()
  const accordionRef = useRef<HTMLDivElement>(null)
  const itemRefs = useRef<(HTMLDivElement | null)[]>([])
  const itemWidthRef = useRef<number>(null)
  const [scrollAreaWidth, setScrollAreaWidth] = useState<number>()

  const handleResize = useCallback(() => {
    if (!itemWidthRef.current) return
    const accordionWidth = getInnerWidth(accordionRef.current)
    const contentWidth = accordionWidth - itemWidthRef.current * items.length
    setScrollAreaWidth(contentWidth)
  }, [items.length])

  useEffect(() => {
    if (!isXlScreen || !itemRefs.current[0] || itemWidthRef.current) return
    itemWidthRef.current = min(itemRefs.current.map(ref => ref?.getBoundingClientRect().width)) || 0
  }, [isXlScreen])

  useResizeObserver({
    ref: accordionRef as RefObject<HTMLElement>,
    onResize: () => {
      if (!isXlScreen) return
      handleResize()
    },
  })

  if (!items.length) return null

  return (
    <AccordionContainer
      type="single"
      defaultValue={items[0].id}
      orientation={isXlScreen ? 'horizontal' : 'vertical'}
      className="grid grid-flow-row xl:grid-flow-col rounded-md bg-gray-200 border border-gray-300 overflow-hidden"
      ref={accordionRef}
    >
      {items.map(({ id, title, text }, index) => (
        <AccordionItem
          key={id}
          value={id}
          ref={ref => {
            itemRefs.current[index] = ref
          }}
          className="grid grid-flow-row xl:grid-flow-col"
        >
          <AccordionTrigger className="xl:[writing-mode:sideways-lr] xl:justify-end bg-gray-300 py-2 px-1 min-w-7 gap-2">
            {title}
          </AccordionTrigger>
          <AccordionContent className="xl:h-96 xl:p-0">
            <ScrollArea
              className="h-full"
              thumbClassName="bg-gray-300"
              style={{ width: isXlScreen && scrollAreaWidth ? scrollAreaWidth : '100%' }}
            >
              <div className="text-gray-600 whitespace-pre-wrap p-2">{text}</div>
            </ScrollArea>
          </AccordionContent>
        </AccordionItem>
      ))}
    </AccordionContainer>
  )
}

export default Accordion
