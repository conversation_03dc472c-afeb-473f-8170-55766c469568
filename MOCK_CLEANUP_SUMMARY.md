# Jest Mock Cleanup Summary

## Overview
This document summarizes the cleanup of unnecessary mocks in the Jest test configuration and test files. The goal was to identify and remove mocks that are not actually needed, making tests simpler and more maintainable.

## Changes Made

### 1. Global Setup Cleanup (`jest.setup.js`)

**Removed Unnecessary Mocks:**
- ❌ `window.scrollTo` - Not used by any tested components
- ❌ `IntersectionObserver` - Not used by any tested components

**Kept Essential Mocks:**
- ✅ `window.matchMedia` - Required by `useIsXlScreen` hook via `useWindowSize`
- ✅ `ResizeObserver` - Required by `useResizeObserver` in Accordion component
- ✅ `Element.prototype.getBoundingClientRect` - Required by Accordion for width calculations
- ✅ `window.getComputedStyle` - Required by `getInnerWidth` utility in Accordion
- ✅ `lucide-react` icons - Required by Accordion and Carousel components
- ✅ `next/font/google` - Required for components that import fonts

### 2. Accordion Test Cleanup (`__tests__/components/Accordion.test.tsx`)

**Removed Unnecessary Mocks:**
- ❌ `lodash.min` - Can use real implementation since it's just `Math.min(...arr.filter(Boolean))`
- ❌ `getInnerWidth` - Can use real implementation since `getComputedStyle` is already mocked globally

**Kept Essential Mocks:**
- ✅ `useIsXlScreen` - Controls component orientation logic (vertical vs horizontal)
- ✅ `useResizeObserver` - Handles resize events for width calculations

### 3. Carousel Test Mocks (No Changes)

**Kept All Mocks:**
- ✅ `embla-carousel-react` - Complex mock with state tracking for interaction tests

### 4. CarouselCard Test Mocks (Minor Cleanup)

**Kept Simplified Mock:**
- ✅ `embla-carousel-react` - Basic mock sufficient for rendering tests (no interaction testing)

## Benefits of Cleanup

### 1. **Reduced Complexity**
- Fewer mocks to maintain and understand
- Clearer separation between what needs mocking vs what can use real implementations

### 2. **Better Test Reliability**
- Using real implementations where possible reduces the chance of mocks diverging from actual behavior
- Less brittle tests that are less likely to break due to mock configuration issues

### 3. **Improved Performance**
- Fewer mock setups and teardowns
- Real implementations are often faster than complex mocks

### 4. **Easier Debugging**
- When tests fail, it's easier to determine if the issue is with the component or the mock
- Real implementations provide more meaningful error messages

## Mock Decision Matrix

| Dependency | Component Usage | Mock Status | Reason |
|------------|----------------|-------------|---------|
| `window.matchMedia` | useIsXlScreen hook | ✅ Required | Browser API not available in jsdom |
| `ResizeObserver` | Accordion resize handling | ✅ Required | Browser API not available in jsdom |
| `getBoundingClientRect` | Accordion width calculations | ✅ Required | Browser API not available in jsdom |
| `getComputedStyle` | getInnerWidth utility | ✅ Required | Browser API not available in jsdom |
| `lucide-react` | Icon components | ✅ Required | Simplifies SVG rendering in tests |
| `embla-carousel-react` | Carousel functionality | ✅ Required | Complex external library |
| `lodash.min` | Accordion calculations | ❌ Removed | Simple utility, real implementation works |
| `getInnerWidth` | Accordion calculations | ❌ Removed | Can use real with mocked getComputedStyle |
| `window.scrollTo` | Not used | ❌ Removed | Unused in current components |
| `IntersectionObserver` | Not used | ❌ Removed | Unused in current components |

## Guidelines for Future Mocks

### When TO Mock:
1. **Browser APIs** not available in jsdom (ResizeObserver, matchMedia, etc.)
2. **Complex external libraries** with side effects (embla-carousel-react)
3. **Network requests** or file system operations
4. **Time-dependent functions** (Date.now, setTimeout, etc.)

### When NOT to Mock:
1. **Simple utility functions** that can run in Node.js
2. **Pure functions** without side effects
3. **Math operations** and basic calculations
4. **String/array manipulation** utilities

### Best Practices:
1. **Start without mocks** and only add them when tests fail
2. **Use real implementations** whenever possible
3. **Mock at the boundary** (external dependencies, not internal utilities)
4. **Keep mocks simple** and focused on the specific behavior being tested
5. **Document why** each mock is necessary

## Test Results
All tests continue to pass after the cleanup:
- ✅ Accordion Component: 13 tests passed
- ✅ Carousel Components: 12 tests passed  
- ✅ CarouselCard Component: 13 tests passed
- ✅ Total: 38 tests, 28 snapshots passed
