import { HTMLAttributes } from 'react'
import { cn } from '@/lib/utils'

export type Props = {
  className?: string
  closeCondition?: boolean
} & HTMLAttributes<HTMLDivElement>

const OverlayLayout = ({ closeCondition, className, ...props }: Props) => {
  return (
    <div
      className={cn(
        '!absolute bottom-0 inset-x-0 !bg-black/75 backdrop-blur-sm z-20 rounded transition-all duration-500 ease-in-out [&_p]:text-white [&_h2]:text-white',
        closeCondition && '!translate-y-full',
        className
      )}
      {...props}
    />
  )
}
export default OverlayLayout
