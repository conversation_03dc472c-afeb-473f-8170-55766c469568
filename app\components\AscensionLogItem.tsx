import { AscensionLog } from '@/app/types/types'
import Heading from '@/app/components/ui/Heading'

type Props = Pick<AscensionLog, 'title' | 'description'>

const AscensionLogItem = ({ title, description }: Props) => {
  return (
    <div className="flex flex-col gap-4 mx-8">
      <Heading as="h3" className="text-base text-gray-700">
        {title}
      </Heading>
      <div className="text-gray-600 my-auto">{description}</div>
    </div>
  )
}

export default AscensionLogItem
