import { LucideIcon } from 'lucide-react'
import Link from 'next/link'

type Props = {
  icon: LucideIcon
  link: string
}

const IconBubble = ({ icon: Icon, link }: Props) => {
  return (
    <Link
      href={link}
      className="border border-gray-700 p-3 rounded-full text-gray-700"
      target="_blank"
    >
      <Icon fill="currentColor" size={24} strokeWidth={0} />
    </Link>
  )
}

export default IconBubble
