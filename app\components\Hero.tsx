import ContactButton from '@/app/components/ContactButton'
import EmailSubscriptionForm from '@/app/components/forms/EmailSubscriptionForm/EmailSubscriptionForm'
import Heading from '@/app/components/ui/Heading'

const Hero = () => {
  return (
    <div className="flex h-screen flex-col justify-center -mt-10 max-xl:border-b-2 text-center">
      <Heading as="h1" className="font-normal">
        manystack
      </Heading>
      <div className="text-gray-500 flex flex-col items-center gap-8">
        <p>Web and Mobile Applications for Success</p>
        <p>
          Our Dream is to build Your Dream.
          <br />
          With ❤️.
        </p>
        <p>
          Your dream project deserves a homely spot to come alive. Join us in a playful alliance:
          <br />
          give way to an app that leaves each one of your users in awe.
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <ContactButton variant="rainbow" FormComponent={EmailSubscriptionForm}>
            Chart your concept!
          </ContactButton>
          <ContactButton variant="outline">Drop a line to start!</ContactButton>
        </div>
      </div>
    </div>
  )
}

export default Hero
