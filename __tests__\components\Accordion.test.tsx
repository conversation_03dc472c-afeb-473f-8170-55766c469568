import React from 'react'
import '@testing-library/jest-dom'
import { render, fireEvent } from '@testing-library/react'
import Accordion from '@/app/components/Accordion'

// Mock the hooks and dependencies that need mocking
jest.mock('@/app/hooks/useIsXlScreen', () => ({
  useIsXlScreen: jest.fn(),
}))

jest.mock('usehooks-ts', () => ({
  useResizeObserver: jest.fn(),
}))

// Import the mocked functions
import { useIsXlScreen } from '@/app/hooks/useIsXlScreen'
import { useResizeObserver } from 'usehooks-ts'

const mockUseIsXlScreen = useIsXlScreen as jest.MockedFunction<typeof useIsXlScreen>
const mockUseResizeObserver = useResizeObserver as jest.MockedFunction<typeof useResizeObserver>

// Mock data for testing
const mockSingleItem = [
  {
    id: 'item-1',
    title: 'Test Item 1',
    text: 'This is the content for test item 1.',
  },
]

const mockMultipleItems = [
  {
    id: 'item-1',
    title: 'Test Item 1',
    text: 'This is the content for test item 1.',
  },
  {
    id: 'item-2',
    title: 'Test Item 2',
    text: 'This is the content for test item 2.',
  },
  {
    id: 'item-3',
    title: 'Test Item 3',
    text: 'This is the content for test item 3.',
  },
]

describe('Accordion Component', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockUseResizeObserver.mockImplementation(() => ({ width: 0, height: 0 }))
  })

  // Helper function to get accordion items
  const getAccordionItems = (container: HTMLElement) => {
    const accordionContainer = container.querySelector('[data-orientation]')
    return Array.from(accordionContainer?.children || []).filter(
      child => child.hasAttribute('data-state') && child.hasAttribute('data-orientation')
    )
  }

  describe('Empty state', () => {
    it('should render null when items array is empty', () => {
      mockUseIsXlScreen.mockReturnValue(false)

      const { container } = render(<Accordion items={[]} />)

      expect(container.firstChild).toBeNull()
    })

    it('should match snapshot for empty items array', () => {
      mockUseIsXlScreen.mockReturnValue(false)

      const { container } = render(<Accordion items={[]} />)

      expect(container).toMatchSnapshot()
    })
  })

  describe('Single item accordion', () => {
    it('should render single item in vertical orientation (mobile)', () => {
      mockUseIsXlScreen.mockReturnValue(false)

      const { container } = render(<Accordion items={mockSingleItem} />)

      expect(container).toMatchSnapshot()
    })

    it('should render single item in horizontal orientation (xl screen)', () => {
      mockUseIsXlScreen.mockReturnValue(true)

      const { container } = render(<Accordion items={mockSingleItem} />)

      expect(container).toMatchSnapshot()
    })
  })

  describe('Multiple items accordion', () => {
    it('should render multiple items in vertical orientation (mobile)', () => {
      mockUseIsXlScreen.mockReturnValue(false)

      const { container } = render(<Accordion items={mockMultipleItems} />)

      expect(container).toMatchSnapshot()
    })

    it('should render multiple items in horizontal orientation (xl screen)', () => {
      mockUseIsXlScreen.mockReturnValue(true)

      const { container } = render(<Accordion items={mockMultipleItems} />)

      expect(container).toMatchSnapshot()
    })
  })

  describe('Screen size variations', () => {
    it('should render correctly when screen size is undefined', () => {
      mockUseIsXlScreen.mockReturnValue(undefined)

      const { container } = render(<Accordion items={mockMultipleItems} />)

      expect(container).toMatchSnapshot()
    })
  })

  describe('Content variations', () => {
    it('should render with long text content', () => {
      const longTextItems = [
        {
          id: 'long-item',
          title: 'Item with Long Content',
          text: 'This is a very long text content that should test how the accordion handles extensive text. '.repeat(
            10
          ),
        },
      ]

      mockUseIsXlScreen.mockReturnValue(false)

      const { container } = render(<Accordion items={longTextItems} />)

      expect(container).toMatchSnapshot()
    })

    it('should render with special characters and formatting', () => {
      const specialItems = [
        {
          id: 'special-item',
          title: 'Special Characters & Formatting',
          text: 'Text with\nnew lines\n\nAnd special chars: @#$%^&*()',
        },
      ]

      mockUseIsXlScreen.mockReturnValue(false)

      const { container } = render(<Accordion items={specialItems} />)

      expect(container).toMatchSnapshot()
    })
  })

  describe('Accordion interaction functionality', () => {
    it('should have first item open by default and others closed', () => {
      mockUseIsXlScreen.mockReturnValue(false)

      const { container } = render(<Accordion items={mockMultipleItems} />)

      // Get all accordion items
      // First item should be open
      const accordionItems = getAccordionItems(container)
      expect(accordionItems[0]).toHaveAttribute('data-state', 'open')
      expect(accordionItems[1]).toHaveAttribute('data-state', 'closed')
      expect(accordionItems[2]).toHaveAttribute('data-state', 'closed')

      // Check aria-expanded attributes on triggers
      const triggers = container.querySelectorAll('button[aria-expanded]')
      expect(triggers[0]).toHaveAttribute('aria-expanded', 'true')
      expect(triggers[1]).toHaveAttribute('aria-expanded', 'false')
      expect(triggers[2]).toHaveAttribute('aria-expanded', 'false')
    })

    it('should open clicked item and close previously open item', () => {
      mockUseIsXlScreen.mockReturnValue(false)

      const { container, getByText } = render(<Accordion items={mockMultipleItems} />)

      // Initially first item is open
      let accordionItems = getAccordionItems(container)
      expect(accordionItems[0]).toHaveAttribute('data-state', 'open')
      expect(accordionItems[1]).toHaveAttribute('data-state', 'closed')
      expect(accordionItems[2]).toHaveAttribute('data-state', 'closed')

      // Click on second item trigger
      const secondTrigger = getByText('Test Item 2')
      fireEvent.click(secondTrigger)

      // After click: first item should be closed, second item should be open
      accordionItems = getAccordionItems(container)
      expect(accordionItems[0]).toHaveAttribute('data-state', 'closed')
      expect(accordionItems[1]).toHaveAttribute('data-state', 'open')
      expect(accordionItems[2]).toHaveAttribute('data-state', 'closed')

      // Check aria-expanded attributes
      const triggers = container.querySelectorAll('button[aria-expanded]')
      expect(triggers[0]).toHaveAttribute('aria-expanded', 'false')
      expect(triggers[1]).toHaveAttribute('aria-expanded', 'true')
      expect(triggers[2]).toHaveAttribute('aria-expanded', 'false')
    })

    it('should handle multiple clicks and track accordion state correctly', () => {
      mockUseIsXlScreen.mockReturnValue(false)

      const { container, getByText } = render(<Accordion items={mockMultipleItems} />)

      // Initial state: first item open
      let accordionItems = getAccordionItems(container)
      expect(accordionItems[0]).toHaveAttribute('data-state', 'open')
      expect(accordionItems[1]).toHaveAttribute('data-state', 'closed')
      expect(accordionItems[2]).toHaveAttribute('data-state', 'closed')

      // Click on third item
      const thirdTrigger = getByText('Test Item 3')
      fireEvent.click(thirdTrigger)

      // After first click: third item should be open
      accordionItems = getAccordionItems(container)
      expect(accordionItems[0]).toHaveAttribute('data-state', 'closed')
      expect(accordionItems[1]).toHaveAttribute('data-state', 'closed')
      expect(accordionItems[2]).toHaveAttribute('data-state', 'open')

      // Click on second item
      const secondTrigger = getByText('Test Item 2')
      fireEvent.click(secondTrigger)

      // After second click: second item should be open
      accordionItems = getAccordionItems(container)
      expect(accordionItems[0]).toHaveAttribute('data-state', 'closed')
      expect(accordionItems[1]).toHaveAttribute('data-state', 'open')
      expect(accordionItems[2]).toHaveAttribute('data-state', 'closed')

      // Click back on first item
      const firstTrigger = getByText('Test Item 1')
      fireEvent.click(firstTrigger)

      // After third click: first item should be open again
      accordionItems = getAccordionItems(container)
      expect(accordionItems[0]).toHaveAttribute('data-state', 'open')
      expect(accordionItems[1]).toHaveAttribute('data-state', 'closed')
      expect(accordionItems[2]).toHaveAttribute('data-state', 'closed')
    })

    it('should work correctly in horizontal orientation (xl screen)', () => {
      mockUseIsXlScreen.mockReturnValue(true)

      const { container, getByText } = render(<Accordion items={mockMultipleItems} />)

      // Initial state: first item open
      let accordionItems = getAccordionItems(container)
      expect(accordionItems[0]).toHaveAttribute('data-state', 'open')
      expect(accordionItems[1]).toHaveAttribute('data-state', 'closed')
      expect(accordionItems[2]).toHaveAttribute('data-state', 'closed')

      // Click on second item trigger
      const secondTrigger = getByText('Test Item 2')
      fireEvent.click(secondTrigger)

      // After click: second item should be open, others closed
      accordionItems = getAccordionItems(container)
      expect(accordionItems[0]).toHaveAttribute('data-state', 'closed')
      expect(accordionItems[1]).toHaveAttribute('data-state', 'open')
      expect(accordionItems[2]).toHaveAttribute('data-state', 'closed')

      // Verify orientation is horizontal
      const accordionContainer = container.querySelector('[data-orientation]')
      expect(accordionContainer).toHaveAttribute('data-orientation', 'horizontal')
    })
  })
})
