import fs from 'fs'
import path from 'path'
import { PageFolder } from '@/app/types/types'
import { replace, trim, split } from 'lodash'

export const readPage = (parentPath: string, folderName: string = '') => {
  const pagePath = path.join(parentPath, folderName, 'page.mdx')
  let title = ''
  let description = ''

  if (fs.existsSync(pagePath)) {
    const [rawTitle = '', rawDescription = ''] = split(
      split(fs.readFileSync(pagePath, 'utf8'), '#')[1],
      /(?:\r\n|\n|\r){2}/
    )
    title = trim(replace(rawTitle, /#/g, ''))
    description = rawDescription
  }

  return { slug: folderName, title, description }
}

const getSubdirectories = (dirPath: string) => {
  return fs
    .readdirSync(dirPath, { withFileTypes: true })
    .filter(dir => dir.isDirectory() && !dir.name.startsWith('['))
    .map(dir => dir.name)
}

export function getPagesByFolder(pathname?: string, searchDeeper?: true): PageFolder[]
export function getPagesByFolder(pathname: string, searchDeeper: false): PageFolder
export function getPagesByFolder(pathname: string = 'services', searchDeeper: boolean = true) {
  const basePath = path.join(process.cwd(), 'app', pathname)

  if (!fs.existsSync(basePath)) return searchDeeper === false ? { slug: '', pages: [] } : []

  try {
    if (!searchDeeper) {
      const pageFolders = getSubdirectories(basePath)

      const folderPage = readPage(basePath)
      const pages = pageFolders.map(folderName => readPage(basePath, folderName))
      const slug = pathname.split('/').pop() || ''

      return { slug, title: folderPage.title, description: folderPage.description, pages }
    }

    const folderFolders = getSubdirectories(basePath)

    return folderFolders.map(folderName => {
      const folderPath = path.join(basePath, folderName)

      const pageFolders = getSubdirectories(folderPath)

      const folderPage = readPage(folderPath)
      const pages = pageFolders.map(subFolderName => readPage(folderPath, subFolderName))

      return {
        slug: folderName,
        title: folderPage.title,
        description: folderPage.description,
        pages,
      }
    })
  } catch (error) {
    console.error('Error reading directory structure:', error)
    return searchDeeper === false ? { slug: '', pages: [] } : []
  }
}
